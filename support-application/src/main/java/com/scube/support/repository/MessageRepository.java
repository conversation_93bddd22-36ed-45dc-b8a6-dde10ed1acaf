package com.scube.support.repository;

import com.scube.support.model.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    Optional<Message> findByUuid(UUID messageUuid);

    Page<Message> findByChatUuid(UUID chatUuid, Pageable pageable);

    Optional<Message> findFirstByChatUuidOrderByCreatedDateDesc(UUID chatUuid);

    List<Message> findByReplyTo_Uuid(UUID replyToMessageUuid);

    List<Message> findBySenderId(UUID senderId);
}
