package com.scube.coordinator.features.templates.dto;

import com.scube.documenttemplates.dto.gen_dto.TemplateDto;
import com.scube.report.features.base.dto.gen_dto.ReportTypeDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetAllTemplates {
    private List<Template> templates;

    public GetAllTemplates(List<TemplateDto> templates, List<ReportTypeDto> reportTypes) {
        Map<String, ReportTypeDto> reportTypeMap = reportTypes.stream()
                .collect(HashMap::new, (map, reportType) -> map.put(reportType.getReportTemplateKey(), reportType), HashMap::putAll);
        this.templates = templates.stream()
                .map(template -> {
                    ReportTypeDto reportType = reportTypeMap.get(template.getNameKey());
                    return new Template(template, reportType);
                })
                .toList();
    }
}