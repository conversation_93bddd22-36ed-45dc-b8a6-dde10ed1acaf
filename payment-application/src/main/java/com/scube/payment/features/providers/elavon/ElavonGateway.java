package com.scube.payment.features.providers.elavon;


import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.lib.misc.dates.DateUtils;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.processing.dto.PayeeDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.rabbit.RefundedEvent;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.receipts.service.ReceiptService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.providers.gateway.IPaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.scube.payment.features.providers.elavon.ElavonProperties.PAYMENT_PROVIDER_NAME;
import static com.scube.payment.features.providers.gateway.PaymentProviderGateway.GATEWAY;
import static org.apache.commons.lang3.StringUtils.defaultString;

@Service(ElavonGateway.NAME)
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class ElavonGateway implements IPaymentProviderGateway<PaymentTokenRequest, PaymentTokenResponse, ElavonEvent> {
    public static final String NAME = PAYMENT_PROVIDER_NAME + GATEWAY;

    private final ElavonHttpClient elavonHttpClient;
    private final PaymentStorageService paymentStorageService;
    private final CalculationServiceConnection calculationServiceConnection;
    private final ReceiptService receiptService;
    private final PaymentProcessingService paymentProcessingService;
    private final AmqpGateway amqpGateway;

    @Override
    public PaymentTokenResponse getToken(PaymentTokenRequest request) {
        log.debug("Getting payment token for order: {}", request.getOrderId());
        var orderId = String.valueOf(request.getOrderId());
        var token = elavonHttpClient.getTransactionToken(orderId, request.getPaymentAmount());

        log.debug("Received payment token from Elavon");
        return new PaymentTokenResponse(token, orderId, PAYMENT_PROVIDER_NAME, Map.of());
    }

    @Override
    public void authCapture(ElavonEvent elavonEvent) {
        log.debug("Processing authCapture webhook: {}", elavonEvent);

        String paymentReference = elavonEvent.getTxnId();
        // If there is an existing paymentReference, it means one of the other payment service replicas
        // has already processed this authcapture.
        if (paymentStorageService.existsByPaymentReference(paymentReference)) {
            return;
        }

        UUID orderId = Optional.ofNullable(elavonEvent.getOrderId())
                .map(UUID::fromString)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Order ID is missing in the Elavon event"));

        OrderInvoiceResponse orderInvoiceResponse = calculationServiceConnection.order().getOrder(orderId);

        PayeeDto payeeDto = PayeeDto.builder()
                .email(defaultString(elavonEvent.getEmail()))
                .phone(defaultString(elavonEvent.getPhone()))
                .firstName(defaultString(elavonEvent.getFirstName()))
                .lastName(defaultString(elavonEvent.getLastName()))
                .mailingAddress(defaultString(elavonEvent.getAddress()))
                .mailingAddress2(defaultString(elavonEvent.getAddress2()))
                .mailingCity(defaultString(elavonEvent.getCity()))
                .mailingState(defaultString(elavonEvent.getState()))
                .mailingZipCode(defaultString(elavonEvent.getZip()))
                .billingSameAsMailing(true)
                .build();

        var transactionDate = Optional.ofNullable(elavonEvent.getTxnTime())
                .map(DateUtils::toInstant)
                .orElseGet(Instant::now);
        SubmitPaymentRequestDto request = SubmitPaymentRequestDto
                .builder()
                .orderId(orderId)
                .orderAmount(orderInvoiceResponse.getTotal())
                .payee(payeeDto)
                .paymentType(elavonEvent.getCardType())
                .paymentAmount(elavonEvent.getAmount())
                .paymentReference(paymentReference)
                .status(PaymentStatus.COMPLETED)
                .transactionDate(transactionDate)
                .authorizedTs(transactionDate)
                .capturedTs(transactionDate)
                .paymentProvider(PAYMENT_PROVIDER_NAME)
                .isOnlineTransaction(true)
                .build();

        SubmitPaymentResponseDto paymentResp = paymentProcessingService.submitPayment(request);

        receiptService.generateReceipt(request, paymentResp.getPaymentId(), orderInvoiceResponse);
    }

    @Override
    public void settle(ElavonEvent request) {
        // TODO: Implement the settle method to handle settlement of authorized transactions.
        // This method should communicate with the Elavon API to finalize the payment.
        throw new UnsupportedOperationException("Not implemented yet");
    }

    @Override
    public void createRefund(RefundTransaction refundTransaction) {
        log.debug("Initiating refund for paymentRef: {}", refundTransaction.getPayment().getPaymentReference());

        String originalTxnId = refundTransaction.getPayment().getPaymentReference();
        BigDecimal refundAmount = BigDecimal.valueOf(refundTransaction.getAmountInCents())
                .divide(BigDecimal.valueOf(100));

        String billingPhone = null;
        if (refundTransaction.getPayment().getPayee() != null) {
            billingPhone = refundTransaction.getPayment().getPayee().getPhone();
        }

        try {
            Map<String, String> refundResult = elavonHttpClient.refundTransaction(originalTxnId, refundAmount, billingPhone);

            String refundTxnId = refundResult.get("ssl_txn_id");
            if (refundTxnId == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,"No transaction ID returned from Elavon");
            }

            log.debug("Refund successful for transaction ID: {}", originalTxnId);
            paymentStorageService.updateRefund(refundTransaction, refundTxnId);

        } catch (Exception e) {
            log.error("Refund failed for Transaction ID: " + originalTxnId, e);
            paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.REJECTED);
        }
    }


    @Override
    public void refund(ElavonEvent elavonEvent) {
        log.debug("Processing refund webhook: {}", elavonEvent);
        String refundReference = elavonEvent.getTxnId();

        RefundTransaction refundTransaction = paymentStorageService.getByRefundReference(refundReference);
        if (refundTransaction == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,"RefundTransaction not found for refundId: " + refundReference);
        }

        paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.COMPLETED);

        OrderInvoiceResponse order = calculationServiceConnection.order().getOrder(refundTransaction.getOrderId());
        BigDecimal refundedAmount = paymentStorageService.getRefundedAmount(refundTransaction.getOrderId());

        amqpGateway.publish(new RefundedEvent(order.getOrderId(), refundedAmount));
    }

    @Override
    public void refundFailed(ElavonEvent elavonEvent) {
        log.debug("Processing failed refund webhook: {}", elavonEvent);
        String refundReference = elavonEvent.getTxnId();

        RefundTransaction refundTransaction = paymentStorageService.getByRefundReference(refundReference);
        if (refundTransaction == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,"RefundTransaction not found for refundReference: " + refundReference);
        }
        paymentStorageService.updateRefundStatus(refundTransaction, RefundStatus.FAILED);
    }

    @Override
    public void _void(ElavonEvent request) {
        // TODO: Implement the _void method to cancel an authorized transaction before settlement.
        // This method should call the Elavon API to void the transaction.
        throw new UnsupportedOperationException("Not implemented yet");
    }

    // RabbitMQ Event for License Status Change
    public record LicenseStatusChangeEvent(UUID licenseEntityId, String status) implements IRabbitFanoutPublisher {}
}