package com.scube.payment.features.providers.authorizedotnet.dto.webhook;

import com.scube.payment.features.webhook.IWebhookPayload;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthorizeDotNetWebhookEventDto implements IWebhookPayload {
    @NotNull
    private UUID notificationId;
    @NotNull
    private AuthorizeDotNetPaymentEvent eventType;
    @NotNull
    private Instant eventDate;
    @NotNull
    private UUID webhookId;
    @NotNull
    private Payload payload;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payload {
        private int responseCode;
        @NotEmpty
        private String authCode;
        @NotEmpty
        private String avsResponse;
        @NotNull
        private BigDecimal authAmount;
        @NotEmpty
        private String invoiceNumber;
        @NotEmpty
        private String entityName;
        private long id;
    }
}
