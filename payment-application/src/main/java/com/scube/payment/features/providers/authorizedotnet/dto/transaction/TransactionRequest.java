package com.scube.payment.features.providers.authorizedotnet.dto.transaction;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.truncate;

@JsonPropertyOrder({"transactionType", "amount", "profile", "order", "lineItems", "customer", "billTo"})
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionRequest<T extends PaymentTokenRequest> {
    private String transactionType;
    private String amount;
    private Profile profile;
    private LineItems lineItems;

    public TransactionRequest(T request) {
        this.transactionType = "authCaptureTransaction";
        this.amount = request.getPaymentAmount().toString();
        this.profile = null;

        List<LineItem> items = request.getItems().stream().map(item -> new LineItem(
                item.getOrderItemId().toString(),
                item.getPrimaryDisplay(),
                item.getSecondaryDisplay(),
                1,
                item.getTotal(),
                false
        )).toList();

        this.lineItems = new LineItems(items);
    }

    public record Profile(String customerProfileId) {
    }

    public record LineItems(List<LineItem> lineItem) {
    }

    @Data
    @JsonPropertyOrder({"itemId", "name", "description", "quantity", "unitPrice", "taxable"})
    public static final class LineItem {
        private String itemId;
        private String name;
        private String description;
        private int quantity;
        private BigDecimal unitPrice;
        private boolean taxable;

        public LineItem(String itemId, @Nullable String name, @Nullable String description, int quantity, BigDecimal unitPrice, boolean taxable) {
            this.itemId = truncate(itemId, 31);
            var splitName = ObjectUtils.isEmpty(name) ? new String[0] : name.split("-");
            if (splitName.length > 1) {
                this.name = truncate(splitName[0], 31);
                this.description = truncate(description + " | " + splitName[1], 255);
            } else {
                this.name = truncate(name, 31);
                this.description = truncate(description, 255);
            }
            this.quantity = quantity;
            this.unitPrice = unitPrice;
            this.taxable = taxable;
        }
    }
}
