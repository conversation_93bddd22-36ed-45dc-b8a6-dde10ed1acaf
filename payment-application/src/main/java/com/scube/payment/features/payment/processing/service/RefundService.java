package com.scube.payment.features.payment.processing.service;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItemFee;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.refund.OrderInvoiceWithRefundTransactionsResponseDto;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.PaginatedResponse;
import com.scube.payment.features.payment.processing.mapper.RefundMapper;
import com.scube.payment.features.payment.processing.rabbit.RefundedEvent;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.model.RefundedDetail;
import com.scube.payment.features.payment.storage.repository.PaymentStorageRepository;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.providers.gateway.PaymentProviderGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;
import com.scube.rabbit.core.AmqpGateway;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.AbstractMap.SimpleEntry;

@Service
@RequiredArgsConstructor
@Slf4j
public class RefundService {

    private final RefundMapper refundMapper;
    private final PaymentStorageService paymentStorageService;
    private final PaymentStorageRepository paymentStorageRepository;
    private final CalculationServiceConnection calculationServiceConnection;
    private final PaymentProviderGateway paymentProviderGateway;
    private final AmqpGateway amqpGateway;

    static class InvalidRefundRequestException extends RuntimeException {
        public InvalidRefundRequestException(String message) {
            super(message);
        }
    }

    private Map<String, BigDecimal> summarizeRefundedHierarchy(UUID orderId, OrderInvoiceResponse order) {
        List<RefundTransaction> existingRefunds = paymentStorageService.getRefundTransactionsByOrderId(orderId);

        if (existingRefunds == null || existingRefunds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<UUID, UUID> feeToItemMap = new HashMap<>();
        Map<UUID, UUID> itemToOrderMap = new HashMap<>();

        for (OrderInvoiceItem item : order.getItems()) {
            UUID itemId = item.getOrderItemUuid();
            itemToOrderMap.put(itemId, order.getOrderId());
            for (OrderInvoiceItemFee fee : item.getFees()) {
                feeToItemMap.put(fee.getOrderItemFeeUuid(), itemId);
            }
        }

        // Step 1: Collect and group refund details
        Map<String, BigDecimal> groupedRefunds = new HashMap<>();
        Set<UUID> directlyRefundedOrders = new HashSet<>();
        Set<UUID> directlyRefundedItems = new HashSet<>();
        Set<UUID> allRefundedItems = new HashSet<>();
        Set<UUID> allRefundedOrders = new HashSet<>();

        for (RefundTransaction refundTransaction : existingRefunds) {
            if (refundTransaction.getStatus() != RefundStatus.COMPLETED &&
                    refundTransaction.getStatus() != RefundStatus.PROCESSING) {
                continue;
            }

            List<RefundedDetail> details = refundTransaction.getRefundedDetail();
            if (details == null) continue;

            for (RefundedDetail detail : details) {
                BigDecimal amount = detail.getRefundedAmount();
                if (amount == null) continue;

                String table = detail.getRefundedParentTable();
                UUID id = detail.getRefundedParentId();

                groupedRefunds.merge(table + ":" + id, amount, BigDecimal::add);

                switch (table) {
                    case "order" -> {
                        directlyRefundedOrders.add(id);
                        allRefundedOrders.add(id);
                    }
                    case "orderItem", "orderAdditionalFee" -> {
                        directlyRefundedItems.add(id);
                        allRefundedItems.add(id);
                        UUID orderUUID = itemToOrderMap.get(id);
                        if (orderUUID != null) allRefundedOrders.add(orderUUID);
                    }
                    case "orderItemFee", "orderAdditionalItemFee" -> {
                        UUID itemId = feeToItemMap.get(id);
                        if (itemId != null) {
                            allRefundedItems.add(itemId);
                            UUID orderUUID = itemToOrderMap.get(itemId);
                            if (orderUUID != null) allRefundedOrders.add(orderUUID);
                        }
                    }
                }
            }
        }

        // Step 2: Aggregate refunds
        Map<UUID, BigDecimal> feeLevelTotals = new HashMap<>();
        Map<UUID, BigDecimal> itemLevelTotals = new HashMap<>();
        Map<UUID, BigDecimal> orderLevelTotals = new HashMap<>();

        for (Map.Entry<String, BigDecimal> entry : groupedRefunds.entrySet()) {
            String[] parts = entry.getKey().split(":");
            String table = parts[0];
            UUID id = UUID.fromString(parts[1]);
            BigDecimal amount = entry.getValue();

            switch (table) {
                case "orderItemFee", "orderAdditionalItemFee" -> {
                    feeLevelTotals.put(id, amount);
                    UUID itemId = feeToItemMap.get(id);
                    if (itemId != null) {
                        itemLevelTotals.merge(itemId, amount, BigDecimal::add);
                        UUID orderUUID = itemToOrderMap.get(itemId);
                        if (orderUUID != null) {
                            orderLevelTotals.merge(orderUUID, amount, BigDecimal::add);
                        }
                    }
                }
                case "orderItem", "orderAdditionalFee" -> {
                    itemLevelTotals.merge(id, amount, BigDecimal::add);
                    UUID orderUUID = itemToOrderMap.get(id);
                    if (orderUUID != null) {
                        orderLevelTotals.merge(orderUUID, amount, BigDecimal::add);
                    }
                }
                case "order" -> orderLevelTotals.merge(id, amount, BigDecimal::add);
            }
        }

        // Step 3: Build result, hiding lower levels if directly refunded at higher levels
        Map<String, BigDecimal> result = new HashMap<>();

        // Show orders (if directly refunded or parent of a refunded item)
        for (Map.Entry<UUID, BigDecimal> entry : orderLevelTotals.entrySet()) {
            UUID orderUUID = entry.getKey();
            if (directlyRefundedOrders.contains(orderUUID) || allRefundedOrders.contains(orderUUID)) {
                result.put(orderUUID.toString(), entry.getValue());
            }
        }

        // Show items (refunded or not), and order is not directly refunded
        for (OrderInvoiceItem item : order.getItems()) {
            UUID itemUUID = item.getOrderItemUuid();
            UUID orderUUID = order.getOrderId();

            if (directlyRefundedOrders.contains(orderUUID)) continue;

            BigDecimal refundedAmount = itemLevelTotals.getOrDefault(itemUUID, BigDecimal.ZERO);
            result.put(itemUUID.toString(), refundedAmount);
        }

        // Show fees (refunded or not), and neither item nor order is directly refunded
        for (OrderInvoiceItem item : order.getItems()) {
            UUID itemUUID = item.getOrderItemUuid();
            UUID orderUUID = order.getOrderId();

            if (directlyRefundedItems.contains(itemUUID) || directlyRefundedOrders.contains(orderUUID)) {
                continue;
            }

            for (OrderInvoiceItemFee fee : item.getFees()) {
                UUID feeUUID = fee.getOrderItemFeeUuid();
                BigDecimal refundedAmount = feeLevelTotals.getOrDefault(feeUUID, BigDecimal.ZERO);
                result.put(feeUUID.toString(), refundedAmount);
            }
        }


        return result;
    }

    private void validateAmount(RefundPaymentRequestDto refundRequest) {
        OrderInvoiceResponse order = calculationServiceConnection.order().getOrder(refundRequest.getPayment().getOrderId());

        Map<String, BigDecimal> itemAmounts = order.getItems().stream()
                .collect(Collectors.toMap(
                        item -> item.getOrderItemUuid().toString(),
                        OrderInvoiceItem::getTotal
                ));

        Map<String, BigDecimal> itemFeeAmounts = order.getItems().stream()
                .flatMap(item -> item.getFees().stream()
                        .map(fee -> new SimpleEntry<>(
                                fee.getOrderItemFeeUuid().toString(),
                                fee.getAmount()
                        )))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        BigDecimal::add
                ));

        itemAmounts.putAll(itemFeeAmounts);
        itemAmounts.put(refundRequest.getOrderId().toString(), order.getTotal());

        Map<String, BigDecimal> summarizeRefundedAmounts = summarizeRefundedHierarchy(refundRequest.getPayment().getOrderId(), order);

        for (var item : refundRequest.getRefundItems()) {
            String parentId = item.getParentId().toString();
            BigDecimal refundAmount = item.getAmount();
            BigDecimal originalAmount = itemAmounts.get(parentId);
            BigDecimal refundedAmount = Optional.ofNullable(summarizeRefundedAmounts)
                    .map(map -> map.getOrDefault(parentId, BigDecimal.ZERO))
                    .orElse(BigDecimal.ZERO);

            if (!itemAmounts.containsKey(parentId)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid parent ID for refund item: " + parentId);
            }

            if (!summarizeRefundedAmounts.isEmpty() && !summarizeRefundedAmounts.containsKey(parentId)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Refund not allowed for:" + parentId + " , Already refunded at a higher level.");
            }

            if (refundedAmount.add(refundAmount).compareTo(originalAmount) > 0) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Refund exceeds allowed limit: already refunded " + refundedAmount +
                                ", attempting to refund " + refundAmount +
                                ", but original amount is only " + originalAmount + " for item: " + parentId
                );
            }
        }
    }

    private void validateRefundRequest(RefundPaymentRequestDto refundRequest) {
        if (ObjectUtils.isEmpty(refundRequest)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Refund request cannot be null or empty.");
        }

        if (ObjectUtils.isEmpty(refundRequest.getPayment())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Payment information is missing in the refund request.");
        }

        if (ObjectUtils.isEmpty(refundRequest.getRefundItems())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No refund items provided in the refund request.");
        }

        if (!refundRequest.getPayment().getOrderId().equals(refundRequest.getOrderId())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Order ID mismatch between payment and refund request.");
        }

        validateAmount(refundRequest);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public RefundPaymentResponseDto refund(RefundPaymentRequestDto refundRequest) {
        var paymentId = refundRequest.getPaymentId();
        Payment payment = paymentStorageRepository.findByUuidOrElseNull(paymentId);
        if (payment == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Payment not found for ID: " + refundRequest.getPaymentId());
        }
        refundRequest.setPayment(payment);
        validateRefundRequest(refundRequest);
        RefundTransaction refundTransaction = paymentStorageService.storeRefund(refundRequest);

        boolean isOffline = !Boolean.TRUE.equals(payment.getIsOnlineTransaction());
        if (isOffline) {
            paymentStorageService.updateOfflineRefund(refundTransaction);
            BigDecimal refundedAmount = paymentStorageService.getRefundedAmount(refundTransaction.getOrderId());
            amqpGateway.publish(new RefundedEvent(refundTransaction.getOrderId(), refundedAmount));
        } else {
            paymentProviderGateway.createRefund(refundTransaction);
        }

        return new RefundPaymentResponseDto(
                refundTransaction.getPayment().getUuid(),
                refundTransaction.getOrderId(),
                refundTransaction.getRefundedTotal(),
                refundTransaction.getStatus()
        );
    }

    @Transactional(readOnly = true)
    public PaginatedResponse<RefundTransaction> getRefundTransaction(int pageNumber, int pageSize, String statusString, LocalDate startDate, LocalDate endDate)
    {
        LocalDate defaultStartDate = (startDate == null) ? LocalDate.of(1900, 1, 1) : startDate;
        LocalDate defaultEndDate = (endDate == null) ? LocalDate.now().plusDays(1) : endDate;

        List<RefundStatus> statuses;
        if (statusString == null) {
            statuses = RefundStatus.getAllStatuses();
        } else {
            var status = RefundStatus.valueOfIgnoreCase(statusString);
            if (status == null)
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid refund status: " + statusString);
            statuses = List.of(status);
        }

        return paymentStorageService.getRefundTransaction(pageNumber, pageSize, statuses, defaultStartDate, defaultEndDate);
    }

    @Transactional(readOnly = true)
    public OrderInvoiceWithRefundTransactionsResponseDto getRefundTransactionsByOrderId(UUID orderId) {
        OrderInvoiceResponse order = calculationServiceConnection.order().getOrder(orderId);
        List<RefundTransaction> refundTransactions = paymentStorageService.getRefundTransactionsByOrderId(orderId);
        List<GetPaymentResponseDto> payments = paymentStorageService.getPaymentResponseDtos(orderId);
        return new OrderInvoiceWithRefundTransactionsResponseDto(order, payments, refundTransactions);
    }

}
