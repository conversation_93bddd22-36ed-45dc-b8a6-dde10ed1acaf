package com.scube.payment.features.providers.authorizedotnet.dto.transaction;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.scube.payment.features.providers.authorizedotnet.dto.config.IAuthorizeDotNetApiResponse;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.Messages;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@JsonPropertyOrder({"transaction", "clientId", "transrefId", "messages"})
public class GetTransactionDetailsResponseDto implements IAuthorizeDotNetApiResponse {
    private Transaction transaction;
    private String clientId;
    private String transrefId;
    private Messages messages;

    @JsonPropertyOrder({"transId", "submitTimeUTC", "submitTimeLocal", "transactionType", "transactionStatus", "responseCode", "responseReasonCode", "responseReasonDescription", "authCode", "AVSResponse", "cardCodeResponse", "batch", "order", "authAmount", "settleAmount", "taxExempt", "billTo", "shipTo", "recurringBilling", "product", "marketType", "networkTransId", "authorizationIndicator"})
    public record Transaction(
            String transId,
            String refTransId,
            String splitTenderId,
            Instant submitTimeUTC,
            String submitTimeLocal,
            String transactionType,
            String transactionStatus,
            String responseCode,
            String responseReasonCode,
            String responseReasonDescription,
            String authCode,
            String AVSResponse,
            String cardCodeResponse,
            Batch batch,
            Order order,
            BigDecimal authAmount,
            BigDecimal settleAmount,
            boolean taxExempt,
            Payment payment,
            BillTo billTo,
            ShipTo shipTo,
            String recurringBilling,
            String product,
            String marketType,
            String networkTransId,
            String authorizationIndicator
    ) {}

    @JsonPropertyOrder({"batchId", "settlementTimeUTC", "settlementTimeLocal", "settlementState"})
    public record Batch(
            String batchId,
            Instant settlementTimeUTC,
            String settlementTimeLocal,
            String settlementState
    ) {}


    public record Order(
            String invoiceNumber,
            String description,
            BigDecimal discountAmount,
            boolean taxIsAfterDiscount
    ) {}

    @JsonPropertyOrder({"creditCard", "bankAccount"})
    public record Payment(
            CreditCard creditCard,
            BankAccount bankAccount
    ) {}

    @JsonPropertyOrder({"cardNumber", "expirationDate", "cardType"})
    public record CreditCard(
            String cardNumber,
            String expirationDate,
            String cardType
    ) {}

    @JsonPropertyOrder({"routingNumber", "accountNumber", "nameOnAccount", "echeckType"})
    public record BankAccount(
            String routingNumber,
            String accountNumber,
            String nameOnAccount,
            String echeckType
    ) {}

    @JsonPropertyOrder({"phoneNumber", "firstName", "lastName", "address", "city", "state", "zip", "country"})
    public record BillTo(
            String phoneNumber,
            String firstName,
            String lastName,
            String address,
            String city,
            String state,
            String zip,
            String country
    ) {}

    @JsonPropertyOrder({"firstName", "lastName", "address", "city", "state", "zip", "country"})
    public record ShipTo(
            String firstName,
            String lastName,
            String address,
            String city,
            String state,
            String zip,
            String country
    ) {}
}


