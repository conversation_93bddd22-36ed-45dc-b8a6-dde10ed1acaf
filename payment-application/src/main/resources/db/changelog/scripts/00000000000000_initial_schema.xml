<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="james (generated)" id="1690895641636-1">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807"
                        minValue="1" sequenceName="fee_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690895641636-2">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807"
                        minValue="1" sequenceName="payment_id_seq" startValue="1"/>
    </changeSet>
    <changeSet author="james (generated)" id="1690895641636-3">
        <createTable tableName="fee">
            <column autoIncrement="true" name="sub_payment_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="fee_pkey"/>
            </column>
            <column name="payment_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="payable_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL"/>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690895641636-4">
        <createTable tableName="payment">
            <column autoIncrement="true" name="payment_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="payment_pkey"/>
            </column>
            <column name="date_paid" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="TEXT"/>
            <column name="total" type="DECIMAL"/>
            <column name="status" type="VARCHAR(20)"/>
            <column name="payment_provider" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="james (generated)" id="1690895641636-5">
        <addForeignKeyConstraint baseColumnNames="payment_id" baseTableName="fee" constraintName="fee_payment_id_fkey"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="payment_id" referencedTableName="payment" validate="true"/>
    </changeSet>
</databaseChangeLog>
