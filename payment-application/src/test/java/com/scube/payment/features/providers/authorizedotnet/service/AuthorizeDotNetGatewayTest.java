package com.scube.payment.features.providers.authorizedotnet.service;

import com.scube.audit.auditable.properties.type.PropertyTypeService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.calculation.generated.IOrderControllerHttpExchangeProxy;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.lib.misc.BeanUtils;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.receipts.service.ReceiptService;
import com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetPaymentProviderSettings;
import com.scube.payment.features.providers.authorizedotnet.config.AuthorizeDotNetProperties;
import com.scube.payment.features.providers.authorizedotnet.dto.config.IAuthorizeDotNetApiResponse;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.GetHostedPaymentPageTokenResponseDto;
import com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment.HostedPaymentSettings;
import com.scube.payment.features.providers.authorizedotnet.dto.mapper.AuthorizeDotNetMapper;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.Message;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.Messages;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.GetTransactionDetailsResponseDto;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.TransactionRequest;
import com.scube.payment.features.providers.authorizedotnet.dto.webhook.AuthorizeDotNetWebhookEventDto;
import com.scube.payment.features.providers.authorizedotnet.exchange.IAuthorizeDotNetXmlExchange;
import com.scube.payment.features.providers.authorizedotnet.model.AuthDotNetMapping;
import com.scube.payment.features.providers.authorizedotnet.repo.AuthDotNetMappingRepository;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthorizeDotNetGatewayTest {
    @Mock
    private IAuthorizeDotNetXmlExchange authorizeDotNetExchange;
    @Mock
    private PaymentProcessingService paymentProcessingService;
    @Mock
    private AuthorizeDotNetMapper mapper;
    @Mock
    private AuthDotNetMappingRepository authDotNetMappingRepository;

    @Mock
    private CalculationServiceConnection calculationServiceConnection;

    @Mock
    private ReceiptService receiptService;

    @InjectMocks
    private AuthorizeDotNetGateway authorizeDotNetGateway;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private PropertyTypeService propertyTypeService;

    @BeforeEach
    void setUp() {
        new BeanUtils().setApplicationContext(applicationContext);
        lenient().when(applicationContext.getBean(PropertyTypeService.class)).thenReturn(propertyTypeService);
        authorizeDotNetGateway.setAuthorizeDotNetProperties(new AuthorizeDotNetProperties("test", "test", "test", null));
        authorizeDotNetGateway.setAuthorizeDotNetPaymentProviderSettings(new AuthorizeDotNetPaymentProviderSettings(Map.of()));
    }

    public static GetTransactionDetailsResponseDto createTestGetTransactionDetailsResponseDto(String transactionId) {
        GetTransactionDetailsResponseDto.Transaction transaction = new GetTransactionDetailsResponseDto.Transaction(
                transactionId, "refTransId", "splitTenderId", Instant.now(), "submitTimeLocal",
                "transactionType", "transactionStatus", "responseCode", "responseReasonCode",
                "responseReasonDescription", "authCode", "AVSResponse", "cardCodeResponse",
                new GetTransactionDetailsResponseDto.Batch("batchId", Instant.now(), "settlementTimeLocal", "settlementState"),
                new GetTransactionDetailsResponseDto.Order("invoiceNumber", "description", BigDecimal.valueOf(10), true),
                BigDecimal.valueOf(100), BigDecimal.valueOf(100), false,
                new GetTransactionDetailsResponseDto.Payment(
                        new GetTransactionDetailsResponseDto.CreditCard("cardNumber", "expirationDate", "cardType"),
                        new GetTransactionDetailsResponseDto.BankAccount("routingNumber", "accountNumber", "nameOnAccount", "echeckType")
                ),
                new GetTransactionDetailsResponseDto.BillTo("phoneNumber", "firstName", "lastName", "address", "city", "state", "zip", "country"),
                new GetTransactionDetailsResponseDto.ShipTo("firstName", "lastName", "address", "city", "state", "zip", "country"),
                "recurringBilling", "product", "marketType", "networkTransId", "authorizationIndicator"
        );

        GetTransactionDetailsResponseDto dto = new GetTransactionDetailsResponseDto();
        dto.setTransaction(transaction);
        dto.setClientId("clientId");
        dto.setTransrefId("transrefId");
        dto.setMessages(new Messages("Ok", List.of(new Message("", ""))));

        return dto;
    }

    @Test
    void validate_validResponse() {
        IAuthorizeDotNetApiResponse response = mock(IAuthorizeDotNetApiResponse.class);
        when(response.getMessages()).thenReturn(new Messages("Ok", List.of(new Message("", ""))));

        assertDoesNotThrow(() -> authorizeDotNetGateway.validate(response));
    }

    @Test
    void validate_nullResponse() {
        IAuthorizeDotNetApiResponse response = null;

        assertThrows(ResponseStatusException.class, () -> authorizeDotNetGateway.validate(response));
    }

    @Test
    void validate_invalidResponse() {
        IAuthorizeDotNetApiResponse response = mock(IAuthorizeDotNetApiResponse.class);
        when(response.getMessages()).thenReturn(new Messages("Error", List.of(new Message("", ""))));

        assertThrows(ResponseStatusException.class, () -> authorizeDotNetGateway.validate(response));
    }

    @Test
    void getTransactionDetails() {
        AuthorizeDotNetGateway authorizeDotNetGateway = spy(this.authorizeDotNetGateway);

        String transactionId = "txn123";
        GetTransactionDetailsResponseDto expectedResponse = createTestGetTransactionDetailsResponseDto(transactionId);
        expectedResponse.setMessages(new Messages("Ok", List.of(new Message("", ""))));

        when(authorizeDotNetExchange.getTransactionDetails(any())).thenReturn(expectedResponse);

        GetTransactionDetailsResponseDto actualResponse = authorizeDotNetGateway.getTransactionDetails(transactionId);

        verify(authorizeDotNetGateway).validate(expectedResponse);
        assertNotNull(actualResponse);
        assertEquals(transactionId, actualResponse.getTransaction().transId());
    }

    @Test
    void getToken() {
        AuthorizeDotNetGateway authorizeDotNetGateway = spy(this.authorizeDotNetGateway);

        PaymentTokenRequest paymentTokenRequest = new PaymentTokenRequest();
        paymentTokenRequest.setOrderId(UUID.randomUUID());
        paymentTokenRequest.setPaymentAmount(BigDecimal.valueOf(20));
        paymentTokenRequest.setOrder(mock(OrderInvoiceResponse.class));
        paymentTokenRequest.getOrder().setItems(List.of(mock(OrderInvoiceItem.class)));

        TransactionRequest transactionRequest = new TransactionRequest(paymentTokenRequest);
        String refId = "refId";

        String expectedKey = "hostedPaymentReturnOptions";
        String expectedValue = "value";
        HostedPaymentSettings.Setting expectedSetting = new HostedPaymentSettings.Setting(expectedKey, expectedValue);
        HostedPaymentSettings mockHostedPaymentSettings = new HostedPaymentSettings(List.of(expectedSetting));

        GetHostedPaymentPageTokenResponseDto response = new GetHostedPaymentPageTokenResponseDto();
        response.setToken("token");
        response.setMessages(new Messages("Ok", List.of(new Message("", ""))));

        when(authorizeDotNetGateway.getHostedPaymentSettings()).thenReturn(mockHostedPaymentSettings);
        when(authorizeDotNetGateway.getRefId(any())).thenReturn(refId);
        when(authorizeDotNetExchange.getHostedPaymentPageToken(any())).thenReturn(response);

        PaymentTokenResponse actualResponse = authorizeDotNetGateway.getToken(paymentTokenRequest);

        verify(authorizeDotNetGateway).getRefId(paymentTokenRequest.getOrderId());
        verify(authorizeDotNetGateway).getHostedPaymentSettings();
        verify(authorizeDotNetGateway).validate(response);
        assertNotNull(actualResponse);
        assertEquals("token", actualResponse.getToken());
        assertEquals(refId, actualResponse.getRefId());
    }

    @Test
    void getRefId() {
        UUID orderId = UUID.randomUUID();
        String expectedRefId = "refId";

        when(authDotNetMappingRepository.getNewRefId()).thenReturn(expectedRefId);

        String actualRefId = authorizeDotNetGateway.getRefId(orderId);

        assertEquals(expectedRefId, actualRefId);
    }

    @Test
    void authCapture() {
        AuthorizeDotNetGateway authorizeDotNetGateway = spy(this.authorizeDotNetGateway);

        AuthorizeDotNetWebhookEventDto authorizeDotNetWebhookEventDto = new AuthorizeDotNetWebhookEventDto();
        authorizeDotNetWebhookEventDto.setPayload(new AuthorizeDotNetWebhookEventDto.Payload());
        authorizeDotNetWebhookEventDto.getPayload().setId(123L);

        GetTransactionDetailsResponseDto expectedResponse = createTestGetTransactionDetailsResponseDto("txn123");
        expectedResponse.setMessages(new Messages("Ok", List.of(new Message("", ""))));

        AuthDotNetMapping authDotNetMapping = new AuthDotNetMapping();
        authDotNetMapping.setOrderId(UUID.randomUUID());
        authDotNetMapping.setRefId("refId");

        SubmitPaymentRequestDto expectedRequest = new SubmitPaymentRequestDto();
        expectedRequest.setOrderId(authDotNetMapping.getOrderId());
        expectedRequest.setOrderAmount(BigDecimal.valueOf(100));
        expectedRequest.setPayee(mapper.toPayee(expectedResponse.getTransaction()));
        expectedRequest.setPaymentType("paymentType");
        expectedRequest.setPaymentAmount(BigDecimal.valueOf(100));
        expectedRequest.setPaymentReference("");
        expectedRequest.setStatus(PaymentStatus.COMPLETED);
        expectedRequest.setTransactionDate(expectedResponse.getTransaction().submitTimeUTC());

        SubmitPaymentResponseDto expectedResponseDto = new SubmitPaymentResponseDto();
        expectedResponseDto.setPaymentId(UUID.randomUUID());

        when(authDotNetMappingRepository.findByRefIdOrThrow("transrefId")).thenReturn(authDotNetMapping);
        when(authorizeDotNetExchange.getTransactionDetails(any())).thenReturn(expectedResponse);
        when(authDotNetMappingRepository.save(any())).thenReturn(authDotNetMapping);
        when(paymentProcessingService.submitPayment(any(SubmitPaymentRequestDto.class))).thenReturn(expectedResponseDto);
        when(calculationServiceConnection.order()).thenReturn(mock(IOrderControllerHttpExchangeProxy.class));
        when(calculationServiceConnection.order().getOrder(any())).thenReturn(mock(OrderInvoiceResponse.class));
        doNothing().when(authorizeDotNetGateway).validate(any());

        authorizeDotNetGateway.authCapture(authorizeDotNetWebhookEventDto);

        verify(authorizeDotNetGateway).getTransactionDetails("123");
        verify(paymentProcessingService).submitPayment(any(SubmitPaymentRequestDto.class));
        verify(authDotNetMappingRepository).save(authDotNetMapping);
    }
}
