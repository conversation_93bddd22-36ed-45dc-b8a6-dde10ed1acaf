package com.scube.licensing.infrastructure.db.entity.participant.contact;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(
        name = ContactGroup.TABLE_NAME,
        indexes = {
                @Index(name = "idx_contact_group_g_name", columnList = ContactGroup.C_NAME)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ContactGroup extends BaseEntity {
    public static final String TABLE_NAME = "contact_group";
    public static final String CONTACT_GROUP_ID = "contact_group_id";
    public static final String C_NAME = "g_name";

    @Size(max = 50)
    @Column(unique = true, name = C_NAME)
    private String name;

    @Size(max = 250)
    private String description;

    public ContactGroup(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isEmail() {
        return "email".equalsIgnoreCase(name);
    }

    public boolean isPhone() {
        return "phone".equalsIgnoreCase(name);
    }
}