package com.scube.licensing.infrastructure.db.entity.merge_request;

import com.scube.licensing.features.merge_request.events.MergeRequestApprovedEvent;
import com.scube.licensing.features.merge_request.events.MergeRequestRejectedEvent;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.envers.Audited;
import org.owasp.encoder.Encode;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Entity
@Table(name = MergeRequest.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
@NoArgsConstructor
public class MergeRequest extends BaseEntity {
    public static final String TABLE_NAME = "merge_request";
    public static final String MERGE_REQUEST_ID = "merge_request_id";

    private UUID requestedUserId;

    @Convert(converter = MergeRequestExistingUserToJsonConverter.class)
    @Column(name = "existing_users", columnDefinition = "jsonb")
    @ColumnTransformer(write = "?::jsonb")
    private List<MergeRequestExistingUser> existingUsers;

    @Size(max = 255)
    private String tagNumber;

    @Size(max = 255)
    private String licenseNumber;

    @Enumerated(EnumType.STRING)
    private MergeRequestStatusEnum status = MergeRequestStatusEnum.PENDING;

    @Size(max = 255)
    private String reason;

    @Size(max = 2000)
    private String deniedComment;

    public MergeRequest(@NonNull UUID requestedUserId, @NonNull List<MergeRequestExistingUser> existingUserIds, @Nullable String tagNumber, @Nullable String licenseNumber) {
        status = MergeRequestStatusEnum.PENDING;
        this.requestedUserId = requestedUserId;
        this.existingUsers = existingUserIds;
        this.tagNumber = tagNumber;
        this.licenseNumber = licenseNumber;
    }

    public void approve() {
        status = MergeRequestStatusEnum.APPROVED;
        this.registerEvent(new MergeRequestApprovedEvent(this));
    }

    public void reject(String reason, String comment) {
        status = MergeRequestStatusEnum.REJECTED;
        this.reason = Encode.forHtml(reason);
        this.deniedComment = Encode.forHtml(comment);
        this.registerEvent(new MergeRequestRejectedEvent(this));
    }

    public Set<UUID> getResidentUUIDs() {
        HashSet<UUID> uuids = new HashSet<>();
        uuids.add(requestedUserId);
        uuids.addAll(existingUsers.stream().map(MergeRequestExistingUser::getExistingUserId).collect(Collectors.toSet()));
        return uuids;
    }

    public String getSearchValue() {
        return ObjectUtils.isEmpty(tagNumber) ? licenseNumber : tagNumber;
    }

    public String getSearchBy() {
        return ObjectUtils.isEmpty(tagNumber) ? "license number" : "tag number";
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}