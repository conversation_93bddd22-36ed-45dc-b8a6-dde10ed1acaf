package com.scube.licensing.infrastructure.db.repository.code_lookup;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CodeLookupRepository extends AuditableEntityRepository<CodeLookup, Long> {
    Optional<CodeLookup> findByCode(@Size(max = 30) String code);

    Optional<CodeLookup> findByCodeAndRealm(@Size(max = 30) String code, @NotBlank String tenant);


    boolean existsByCode(@Size(max = 30) String code);

    List<CodeLookup> findAllByEntityTypeAndEntityId(@Size(max = 255) String entityType, @Size(max = 255) String entityId);

    List<CodeLookup> findAllByEntityTypeAndEntityIdAndAction(@Size(max = 255) String entityType, @Size(max = 255) String entityId, CodeLookupActionEnum action);

    Optional<CodeLookup> findFirstByEntityTypeAndEntityId(@Size(max = 255) String entityType, @Size(max = 255) String entityId);

    Optional<CodeLookup> findFirstByEntityTypeAndEntityIdAndActionAndRealm(@Size(max = 255) String entityType, @Size(max = 255) String entityId, CodeLookupActionEnum action, @NotBlank String tenant);

    Optional<CodeLookup> findFirstByActionAndRealm(@Size(max = 255) CodeLookupActionEnum action, @NotBlank String tenant);

    boolean existsByEntityTypeAndEntityId(@Size(max = 255) String entityType, @Size(max = 255) String entityId);
}