package com.scube.licensing.infrastructure.db.entity.profile_builder;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(
        name = ProfileType.TABLE_NAME,
        indexes = {
                @Index(name = "idx_profile_name", columnList = ProfileType.P_NAME)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ProfileType extends BaseEntity {
    public static final String TABLE_NAME = "profile_type";
    public static final String PROFILE_TYPE_ID = "profile_type_id";
    public static final String P_NAME = "name";

    @Size(max = 255)
    @Column(name = P_NAME, nullable = false)
    private String name;

    @Size(max = 255)
    private String description;

    public ProfileType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}