package com.scube.licensing.features.document.service;

import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.licensing.features.association.delete_association.DeleteAllAssociationsCommand;
import com.scube.licensing.features.association.save_association.SaveAssociationCommand;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.mapper.DocumentDtoMapper;
import com.scube.licensing.features.profile.dto.ProfileAndAssociationsDto;
import com.scube.licensing.features.profile.get_profile_and_associations.GetProfileAndAssociationsDtoQuery;
import com.scube.licensing.features.profile.get_profile_and_associations.GetProfileAndAssociationsQuery;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.document.DocumentType;
import com.scube.licensing.infrastructure.db.repository.document.DocumentRepository;
import com.scube.licensing.infrastructure.db.repository.document.DocumentTypeRepository;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class DocumentService {
    private final DocumentRepository documentRepository;
    private final DocumentTypeRepository documentTypeRepository;
    private final DocumentStorageConnectionService documentStorageConnectionService;
    private final DocumentServiceConnection documentServiceConnection;
    private final DocumentHistoryService documentHistoryService;
    private final DocumentDtoMapper documentMapper;
    private final AxonGateway axonGateway;

    /**
     * Get all documents associated with an entity.
     *
     * @param associable
     * @return
     */
    public List<DocumentDto> getDocuments(Associable associable) {
        return associable.getDocuments()
                .stream()
                .map(documentMapper::toDto)
                .toList();
    }

    /**
     * Get all documents associated with an entity.
     *
     * @param entityType
     * @param entityId
     * @return
     */
    public List<DocumentDto> getDocuments(String entityType, UUID entityId) {
        ProfileAndAssociationsDto resp = axonGateway.query(new GetProfileAndAssociationsDtoQuery(entityType, entityId));

        return resp.getAssociations().stream()
                .filter(DocumentDto.class::isInstance)
                .map(DocumentDto.class::cast)
                .toList();
    }

    /**
     * Get a document by its ID.
     *
     * @param id
     * @return
     */
    public Document getDocument(Long id) {
        return documentRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Document not found"));
    }

    /**
     * Add a new document.
     *
     * @param key
     * @param file
     * @return
     */
    @Transactional
    public Document addNew(String key, MultipartFile file) {
        DocumentType documentType = documentTypeRepository.findByKeyIgnoreCase(key).orElseThrow(
                () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Document type not found")
        );

        var documentStorageResponse = documentStorageConnectionService.uploadFile(file);

        Document document = new Document();

        document.setDocumentType(documentType)
                .setContentType(file.getContentType())
                .setFileName(file.getOriginalFilename())
                .setUrl(documentStorageResponse.getDocumentUrl())
                .setSize(file.getSize())
                .setDocumentServiceUuid(documentStorageResponse.getDocumentUUID());

        return documentRepository.save(document);
    }

    /**
     * Update an existing document.
     *
     * @param document
     * @param file
     * @return
     */
    @Transactional
    public Document update(Document document, MultipartFile file) {
        var existingDocument = documentRepository.findByIdOrThrow(document.getId()); // Needed since the document is detached

        var documentStorageResponse = documentStorageConnectionService.uploadFile(file);

        existingDocument
                .setContentType(file.getContentType())
                .setFileName(file.getOriginalFilename())
                .setUrl(documentStorageResponse.getDocumentUrl())
                .setSize(file.getSize())
                .setDocumentServiceUuid(documentStorageResponse.getDocumentUUID())
                .setDeleted(false);

        return documentRepository.save(existingDocument);
    }

    /**
     * Get a document by its entity ID.
     *
     * @param entityId
     * @return
     */
    public Document getDocumentByEntityIdOrElseThrow(UUID entityId) {
        return documentRepository.findByUuid(entityId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Document not found"));
    }

    /**
     * Get a document by its entity ID.
     *
     * @param entityId
     * @return
     */
    public Optional<Document> getDocumentByEntityId(UUID entityId) {
        return documentRepository.findByUuid(entityId);
    }


    /**
     * Get a document DTO by its entity ID.
     *
     * @param entityId
     * @return
     */
    public DocumentDto getDto(UUID entityId) {
        return documentMapper.toDto(getDocumentByEntityIdOrElseThrow(entityId));
    }

    /**
     * Soft delete a document by its entity ID.
     *
     * @param entityIds
     */
    @Transactional
    public void softDelete(UUID... entityIds) {
        for (UUID entityId : entityIds) {
            Document document = documentRepository.findByUuid(entityId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Document not found"));

            document.setDeletedDate(Instant.now());
            document.setDeleted(true);

            documentRepository.save(document);
        }
    }

    /**
     * Hard delete a document by its entity ID.
     *
     * @param entityId
     */
    @Transactional
    public void hardDelete(UUID entityId) {
        Document document = documentRepository.findByUuid(entityId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Document not found"));

        documentRepository.delete(document);

        axonGateway.sendAndWait(new DeleteAllAssociationsCommand(document));
    }

    public Map<String, MultipartFile> processFieldToBeDeleted(Map<String, String> fields, Map<String, MultipartFile> files) {
        var result = new HashMap<String, MultipartFile>();
        documentTypeRepository.findAll().forEach(documentType -> {
            if (fields.containsKey(documentType.getKey())) {
                fields.remove(documentType.getKey());
                result.put(documentType.getKey(), null);
            }
        });
        result.putAll(files);
        return result;
    }

    @Transactional
    public List<DocumentDto> upload(Associable associable, Map<String, MultipartFile> files) {
        if (ObjectUtils.isEmpty(files)) return List.of();
        return upload(associable.getEntityType(), associable.getUuid(), files);
    }

    /**
     * Upload a document.
     *
     * @param entityType
     * @param entityId
     * @param files
     * @return
     */
    @Transactional
    public List<DocumentDto> upload(String entityType, UUID entityId, Map<String, MultipartFile> files) {
        List<DocumentDto> existingDocuments = getDocuments(entityType, entityId);

        List<Document> newDocuments = new ArrayList<>();
        List<Document> updatedDocuments = new ArrayList<>();

        for (Map.Entry<String, MultipartFile> entry : files.entrySet()) {
            String key = entry.getKey();
            MultipartFile value = entry.getValue();
            Optional<DocumentDto> existingDocument = existingDocuments.stream()
                    .filter(d -> d.getKey().equals(key))
                    .findFirst();

            if (ObjectUtils.isEmpty(value) || value.getSize() == 0) {
                //delete the document
                existingDocument.ifPresent(documentDto -> softDelete(documentDto.getEntityId()));
                continue;
            }

            if (existingDocument.isPresent()) {
                Document updatedDocument = update(documentMapper.toEntity(existingDocument.get()), value);
                updatedDocuments.add(updatedDocument);
            } else {
                Document newDocument = addNew(key, value);
                newDocuments.add(newDocument);
            }
        }

        saveAssociations(entityType, entityId, newDocuments);

        List<Document> allDocuments = new ArrayList<>();
        allDocuments.addAll(newDocuments);
        allDocuments.addAll(updatedDocuments);

        return allDocuments.stream()
                .map(documentMapper::toDto)
                .collect(Collectors.toList());
    }

    public Resource download(@NonNull Document document) {
        return documentServiceConnection.document().getFile(document.getDocumentServiceUuid());
    }

    /**
     * Get the history of a document by its entity ID.
     *
     * @param entityId
     * @param pageable
     * @return
     */
    public Page<DocumentDto> getHistory(UUID entityId, Pageable pageable) {
        var documentHistory = documentHistoryService.getDocumentHistoryByEntityId(entityId, pageable);

        return documentHistory.map(documentMapper::toDto);
    }

    /**
     * Save associations between a document and other entities.
     *
     * @param entityType
     * @param entityId
     * @param documents
     */
    public void saveAssociations(String entityType, UUID entityId, List<Document> documents) {
        var resp = axonGateway.query(new GetProfileAndAssociationsQuery(entityType, entityId));

        var associations = filterAssociations(resp.profile(), resp.associations());

        documents.forEach(document -> {
            axonGateway.sendAndWait(new SaveAssociationCommand(resp.profile(), document));
            axonGateway.sendAndWait(new SaveAssociationCommand(document, resp.profile()));

            for (Associable association : associations) {
                axonGateway.sendAndWait(new SaveAssociationCommand(document, association));
                axonGateway.sendAndWait(new SaveAssociationCommand(association, document));
            }
        });
    }

    /**
     * Filter profile associations based on which need to be associated with the document.
     * Stubbing for now as we don't have the business rules yet.
     *
     * @param profile
     * @param associations
     * @return
     */
    public static List<Associable> filterAssociations(Associable profile, List<Associable> associations) {
        return new ArrayList<>();
    }
}