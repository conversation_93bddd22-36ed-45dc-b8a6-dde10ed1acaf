package com.scube.licensing.features.license;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.license.change_status.ChangeStatusCommand;
import com.scube.licensing.features.license.fee.GetAllLicenseFeeQueryHandler;
import com.scube.licensing.features.license.fee.GetAllLicenseFeesQuery;
import com.scube.licensing.features.license.fee.sql_fees.FeeCalculationByDurationResponse;
import com.scube.licensing.features.license.fee.sql_fees.LicenseFeeService;
import com.scube.licensing.features.license.license_actions_query.GetLicenseActionsQueryHandler;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseCommand;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseResponse;
import com.scube.licensing.features.license.partial_save.create_initial_license.CreateInitialLicenseCommand;
import com.scube.licensing.features.license.partial_save.create_initial_license.CreateInitialLicenseResponse;
import com.scube.licensing.features.license.remove_participant.RemoveParticipantCommand;
import com.scube.licensing.features.license.renewals.LicenseRenewalCommand;
import com.scube.licensing.features.license.validation.CheckLicenseExists;
import com.scube.licensing.features.license.validation.CheckLicenseStatusExists;
import com.scube.licensing.features.license.validation.CheckLicenseTypeExists;
import com.scube.licensing.features.participant.dog.DogService;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.participant.validation.CheckParticipantExists;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.validation.NullOrUndefinedToNull;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("license")
@Slf4j
@Validated
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class LicenseController {
    private final AxonGateway axonGateway;
    private final AmqpGateway amqpGateway;
    private final LicenseActivityService licenseActivityService;
    private final LicenseService licenseService;
    private final DogService dogService;
    private final LicenseFeeService licenseFeeService;

    @PostMapping(value = "create", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.License.CREATE_LICENSE)
    public CreateLicenseResponse createLicense(@RequestParam @CheckParticipantExists UUID participantId,
                                               @RequestParam @CheckLicenseTypeExists @Size(max = 255) String licenseType,
                                               @RequestParam @NullOrUndefinedToNull Map<String, String> fields,
                                               @RequestParam Map<String, MultipartFile> files,
                                               @RequestParam(required = false, defaultValue = "1") @Min(0) @Max(3) Integer duration,
                                               @RequestParam(required = false) @Min(2000) @Max(2099) Integer startYear,
                                               @RequestParam(required = false) @Min(2000) @Max(2099) Integer endYear) {
        CreateInitialLicenseResponse pendingLicense = createPendingLicense(new CreateInitialLicenseCommand(participantId, licenseType));
        CreateParticipantResponseDTO dog = addDogToLicense(pendingLicense.entityId(), fields, files);
        CreateFinalLicenseResponse finalLicense = createFinalLicense(pendingLicense.entityId(), duration, startYear, endYear);

        return new CreateLicenseResponse(finalLicense.getEntityType(), finalLicense.getEntityId(), dog.getEntityType(), dog.getEntityId(), dog.getDocuments());
    }

    @PostMapping("pending")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.License.CREATE_PENDING_LICENSE)
    public CreateInitialLicenseResponse createPendingLicense(@RequestBody @Valid final CreateInitialLicenseCommand command) {
        return axonGateway.sendAndWait(command);
    }

    @PostMapping("final/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.CREATE_FINAL_LICENSE)
    public CreateFinalLicenseResponse createFinalLicense(@PathVariable @CheckLicenseExists final UUID entityId,
                                                         @RequestParam(required = false, defaultValue = "1") @Min(0) @Max(3) Integer duration,
                                                         @RequestParam(required = false) @Min(2000) @Max(2099) Integer startYear,
                                                         @RequestParam(required = false) @Min(2000) @Max(2099) Integer endYear) {
        return axonGateway.sendAndWait(new CreateFinalLicenseCommand(entityId, duration, true, startYear, endYear));
    }

    @GetMapping("{licenseEntityId}/outstanding-approvals")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_OUTSTANDING_APPROVALS)
    public List<String> getOutstandingApprovals(@PathVariable @CheckLicenseExists final UUID licenseEntityId) {
        return licenseService.getOutstandingApprovals(licenseEntityId);
    }

    @PostMapping("{licenseEntityId}/mark-approved")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.MARK_LICENSE_AS_APPROVED)
    public void markLicenseAsApproved(@PathVariable @CheckLicenseExists final UUID licenseEntityId) {
        licenseService.markAsApproved(licenseEntityId);
    }

    @PostMapping("{licenseEntityId}/mark-pending-approval")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.MARK_LICENSE_AS_PENDING_APPROVAL)
    public void markLicenseAsPendingApproval(@PathVariable @CheckLicenseExists final UUID licenseEntityId) {
        licenseService.markAsPendingApproval(licenseEntityId);
    }

    @PostMapping("{licenseEntityId}/mark-rejected")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.MARK_LICENSE_AS_DENIED)
    public void markLicenseAsDenied(@PathVariable @CheckLicenseExists final UUID licenseEntityId, @RequestBody MarkLicenseAsRejectedRequest request) {
        licenseService.markAsRejected(licenseEntityId, request.reason(), request.comment());
    }

    @PatchMapping("status/{entityId}/{status}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.CHANGE_LICENSE_STATUS)
    public void changeLicenseStatus(@PathVariable("entityId") @CheckLicenseExists final UUID entityId, @PathVariable("status") @CheckLicenseStatusExists @Size(max = 255) final String status) {
        axonGateway.sendAndWait(new ChangeStatusCommand(entityId, status));
    }

    @PostMapping(value = "{licenseEntityId}/add-dog", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.License.ADD_DOG_TO_LICENSE)
    public CreateParticipantResponseDTO addDogToLicense(@PathVariable final UUID licenseEntityId, @RequestParam @NullOrUndefinedToNull Map<String, String> fields, @RequestParam Map<String, MultipartFile> files) {
        return dogService.addDogToLicense(licenseEntityId, fields, files);
    }

    @PatchMapping("{entityId}/participant/{participantId}/remove")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.REMOVE_PARTICIPANT)
    public ResponseEntity<Void> removeParticipant(@PathVariable("entityId") final UUID entityId, @PathVariable("participantId") final UUID participantId) {
        axonGateway.sendAndWait(new RemoveParticipantCommand(entityId, participantId));
        return ResponseEntity.noContent().build();
    }

    @GetMapping("{entityId}/fees")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_LICENSE_FEES)
    public ResponseEntity<GetAllLicenseFeeQueryHandler.GetAllLicenseFeeResponse> getLicenseFees(@PathVariable final UUID entityId) {
        var result = amqpGateway.queryResult(new GetAllLicenseFeesQuery(entityId));
        if (result.isFailure())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, result.getErrorMessage());
        return ResponseEntity.ok(result.getResult());
    }

    @GetMapping("{licenseEntityId}/fees/calculate")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.CALCULATE_LICENSE_FEES)
    public FeeCalculationByDurationResponse calculateLicenseFees(@PathVariable final UUID licenseEntityId) {
        return licenseFeeService.getFeePreviews(licenseEntityId, false);
    }

    @PostMapping("{entityId}/renew")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.RENEW_LICENSE)
    public void renewLicense(@PathVariable final UUID entityId,
                             @RequestParam(required = false, defaultValue = "1") @Min(0) @Max(3) Integer duration,
                             @RequestParam(required = false) @Min(2000) @Max(2099) Integer startYear,
                             @RequestParam(required = false) @Min(2000) @Max(2099) Integer endYear
    ) {
        axonGateway.sendAndWait(new LicenseRenewalCommand(entityId, duration, true, startYear, endYear));
    }

    @GetMapping("{entityId}/actions")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_LICENSE_ACTIONS)
    public GetLicenseActionsQueryHandler.GetLicenseActionsQueryResponse getLicenseActions(@PathVariable final UUID entityId) {
        var result = amqpGateway.queryResult(new GetLicenseActionsQuery(entityId));
        if (result.isFailure())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, result.getErrorMessage());
        return result.getResult();
    }

    @GetMapping("{entityId}/vaccination-expired")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.IS_VACCINATION_EXPIRED)
    public IsVaccinationExpiredQueryResponse isVaccinationExpired(@PathVariable final UUID entityId) {
        var result = amqpGateway.queryResult(new IsVaccinationExpiredQuery(entityId));
        if (result.isFailure())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, result.getErrorMessage());
        return result.getResult();
    }

    @DeleteMapping("{entityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.License.DELETE_LICENSE_DRAFT)
    public void deleteLicenseDraft(@PathVariable("entityId") final UUID entityId) {
        //amqpGateway.publish(new DeleteLicenseCommand(entityId));
        licenseService.undoRenewal(entityId);
    }

    @PostMapping("re-process-fee/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.RE_PROCESS_FEE)
    public CreateFinalLicenseResponse reProcessFee(@PathVariable @CheckLicenseExists final UUID entityId, @RequestParam("licenseActivityId") final Long licenseActivityId) {
        if (ObjectUtils.isEmpty(licenseActivityId))
            return licenseActivityService.removeAndReIssueUnpaidActivityFees(entityId, licenseActivityId, false);
        return licenseActivityService.removeAndReIssueUnpaidActivityFees(entityId, true);
    }

    @GetMapping("query")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.QUERY)
    public Page<?> query(@PageableDefault(size = 10) Pageable pageable, @RequestParam Map<String, Object> searchParams) {
        return licenseService.query(pageable, searchParams);
    }

    @GetMapping("{licenseEntityId}/license-form")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_LICENSE_FORM)
    public LicenseService.GetLicenseFormResponse getLicenseForm(@PathVariable final UUID licenseEntityId) {
        return licenseService.getLicenseFormResponse(licenseEntityId);
    }

    // @formatter:off
    public record IsVaccinationExpiredQuery(UUID licenseEntityId) implements IRabbitFanoutPublisherRpc<IsVaccinationExpiredQueryResponse> {}
    public record IsVaccinationExpiredQueryResponse(Boolean isVaccinationExpired) {}
    public record GetLicenseActionsQuery(UUID entityId) implements IRabbitFanoutPublisherRpc<GetLicenseActionsQueryHandler.GetLicenseActionsQueryResponse> {}
    public record DeleteLicenseCommand(UUID entityId) implements IRabbitFanoutPublisher {}
    public record MarkLicenseAsRejectedRequest(@NonNull String reason, String comment) {}
    // @formatter:on
}