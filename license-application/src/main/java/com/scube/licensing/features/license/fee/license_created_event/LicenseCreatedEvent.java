package com.scube.licensing.features.license.fee.license_created_event;

import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LicenseCreatedEvent implements IRequestVoidAxon {
    private UUID licenseEntityId;
    private String licenseType;
    private boolean autoApproval;
}
