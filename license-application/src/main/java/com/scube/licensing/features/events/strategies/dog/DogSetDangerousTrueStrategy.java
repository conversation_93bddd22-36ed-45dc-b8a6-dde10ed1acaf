package com.scube.licensing.features.events.strategies.dog;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.licensing.features.document.service.DocumentStorageConnectionService;
import com.scube.licensing.features.events.dto.EventRequestDto;
import com.scube.licensing.features.events.service.util.EventLicenseFormService;
import com.scube.licensing.features.events.strategies.IEventStrategyService;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Service("dogSetDangerousTrue")
@Slf4j
@AllArgsConstructor
public class DogSetDangerousTrueStrategy implements IEventStrategyService {
    private final DocumentStorageConnectionService documentStorageConnectionService;
    private final EventLicenseFormService licenseFormService;
    private final ParticipantService participantService;

    @Override
    public Object execute(Associable profile, EventRequestDto dto) {
        Participant dog = (Participant) profile;

        //Process all updates as a transaction before generating license forms
        transactionalUpdate(profile, dto);

        licenseFormService.regenerateLicenseForms(dog);

        return participantService.toDto(dog);
    }

    @Transactional
    private void transactionalUpdate(Associable profile, EventRequestDto dto) {
        //Store any court order documents
        for (Map.Entry<String, MultipartFile> entry : dto.files().entrySet()) {
            var documentId = documentStorageConnectionService.uploadFile(entry.getValue());

            profile.setProperty(PropertyTypeEnum.STRING, entry.getKey(), documentId);
        }

        //Set isDangerous to true on the dog
        profile.setProperty(PropertyTypeEnum.BOOLEAN, "isDangerous", true);
    }
}