package com.scube.licensing.features.license.remove_participant;

import com.scube.licensing.features.association.delete_association.DeleteAssociationByEntityCommand;
import com.scube.licensing.features.association.delete_association.DeleteAssociationCommand;
import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentQuery;
import com.scube.licensing.features.association.find_all_parent_associations_by_child.FindAllParentAssociationsByChildQuery;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import lombok.RequiredArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
public class RemoveParticipantHandler implements IRequestHandlerVoidAxon<RemoveParticipantCommand> {
    private final AxonGateway axonGateway;

    @Override
    @CommandHandler
    public void handle(RemoveParticipantCommand command) {
        var license = axonGateway.query(new LicenseService.FindLicenseByEntityIdQuery(command.getEntityId()))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, String.format("License with id %s not found", command.getEntityId())));
        var participant = axonGateway.query(new ParticipantService.FindParticipantByEntityIdQuery(command.getParticipantId()))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, String.format("Participant with id %s not found", command.getParticipantId())));

        axonGateway.sendAndWait(new DeleteAssociationByEntityCommand(license, participant));
        axonGateway.sendAndWait(new DeleteAssociationByEntityCommand(participant, license));

        // if a dog is remove from a license then we need to remove it from the participant
        List<Association> parentParticipantAssociations = axonGateway.query(new FindAllParentAssociationsByChildQuery(participant));
        List<Association> childParticipantAssociations = axonGateway.query(new FindAllChildAssociationsByParentQuery(participant));
        //combine the parent and child associations
        var associations = Stream.of(parentParticipantAssociations, childParticipantAssociations)
                .flatMap(List::stream)
                .toList();
        for (var assoc : associations) {
            if (assoc.getParentAssociationType().equals(AssociationType.PARTICIPANT)) {
                axonGateway.sendAndWait(new DeleteAssociationCommand(assoc));
            }
        }
    }
}
