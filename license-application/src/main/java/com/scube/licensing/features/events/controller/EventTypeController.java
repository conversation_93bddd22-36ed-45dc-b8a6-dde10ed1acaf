package com.scube.licensing.features.events.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.events.dto.EventTypeDto;
import com.scube.licensing.features.events.service.EventTypeService;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

@RestController
@RequestMapping("event-type")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class EventTypeController {
    private final EventTypeService eventTypeService;

    @GetMapping
    @RolesAllowed(Permissions.EventType.GET_EVENT_TYPE)
    public EventTypeDto getEventType(@RequestParam(required = false) Long eventTypeId,
                                     @RequestParam(required = false) @Size(max = 255) String eventTypeCode) {
        if (!ObjectUtils.isEmpty(eventTypeId))
            return eventTypeService.getEventType(eventTypeId);

        if (!ObjectUtils.isEmpty(eventTypeCode))
            return eventTypeService.getEventType(eventTypeCode);

        throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "One of the parameters must be provided");
    }

    @GetMapping("dropdown")
    @RolesAllowed(Permissions.EventType.GET_EVENT_TYPE_FOR_DROP_DOWN)
    public List<EventTypeDto> getEventTypeForDropDown() {
        List<String> allowedTypes = List.of(
                // dog
                "dogDeceased", "dogLost", "dogRelinquished", "dogFound", "dogReactivated",
                "dogTransferOfOwnership", "dogSetDangerousFalse", "dogSetDangerousTrue",
                // resident
                "individualDeceased", "individualMovedOutsideJurisdiction", "individualReactivated"
        );
        return eventTypeService.getEventTypes().stream()
                .filter(eventTypeDto -> allowedTypes.contains(eventTypeDto.getCode()))
                .toList();
    }

    @GetMapping("all")
    @RolesAllowed(Permissions.EventType.GET_EVENT_TYPES)
    public List<EventTypeDto> getEventTypes() {
        return eventTypeService.getEventTypes();
    }

    @GetMapping("profile-type/{profileType}")
    @RolesAllowed(Permissions.EventType.GET_EVENT_TYPES_BY_PROFILE_TYPE)
    public List<EventTypeDto> getEventTypesByProfileType(@PathVariable @Size(max = 255) String profileType) {
        return eventTypeService.getByProfileType(profileType);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.EventType.CREATE_EVENT_TYPE)
    public EventTypeDto createEventType(@RequestBody EventTypeDto dto) {
        return eventTypeService.createEventType(dto);
    }
}