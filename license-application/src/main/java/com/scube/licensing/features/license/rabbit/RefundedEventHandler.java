package com.scube.licensing.features.license.rabbit;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.licensing.features.license.LicenseService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@RequiredArgsConstructor
@Slf4j
public class RefundedEventHandler extends FanoutListener<RefundedEvent> {
    private final LicenseService licenseService;
    private final CalculationServiceConnection calculationServiceConnection;

    @Override
    @Transactional
    public void consume(RefundedEvent event) {
        log.info("RefundedEventHandler received event: orderId={}, refundedAmount={}", event.orderId(), event.refundedAmount());
        OrderInvoiceResponse order = calculationServiceConnection.order().getOrder(event.orderId());

        if (event.refundedAmount().compareTo(order.getTotal()) == 0) {
            order.setStatus("orderRefunded");
            licenseService.updateLicenseActivityFeeAsUnPaid(order);
        } else if (event.refundedAmount().compareTo(BigDecimal.ZERO) > 0) {
            order.setStatus("orderPartiallyRefunded");
            licenseService.updateLicenseActivityFeeAsPartiallyPaid(order);
        }

        order.getItems().stream()
                .filter(item -> "license".equals(item.getItemType()))
                .map(OrderInvoiceItem::getItemId)
                .forEach(itemId -> {
                    licenseService.changeStatus(itemId, "Refunded", null, true);
                });
    }
}
