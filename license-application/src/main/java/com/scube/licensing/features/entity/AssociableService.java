package com.scube.licensing.features.entity;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.licensing.features.address.service.AddressService;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.entity.dtos.CreateAddressRequestDto;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AssociableService {
    private final AddressService addressService;
    private final DocumentService documentService;

    public <T extends EntityRequest<?>> void upsertAssociable(Associable associable, T request) {
        addAssociations(associable, request);

        for (CreateAddressRequestDto addr : request.getAddresses()) {
            Address address = addressService.upsertAddress(addr);
            associable.addBiDirectionalAssociable(address, Map.of("addressType", addr.getType()));
        }

        for (CreateCustomFieldsDto custom : request.getCustomFields()) {
            PropertyTypeEnum type = PropertyTypeEnum.fromValue(custom.getType());
            associable.setProperty(type, custom.getKey(), custom.getValue());
        }
    }

    public <T extends EntityRequest<?>> void updateAssociable(Associable associable, T request) {
        associable.removeAllAssociables();
        upsertAssociable(associable, request);
    }

    public <T extends IEntityRequest> void addAssociations(Associable associable, T request) {
        log.info("Adding associations for entity: {}", request);
        for (EntityAssociation assoc : request.getAssociations()) {
            Map<String, Object> properties = assoc.getCustomFields()
                    .stream().collect(HashMap::new, (map, field) -> map.put(field.getKey(), field.getValue()), HashMap::putAll);
            associable.addBiDirectionalAssociable(assoc.getEntityType(), assoc.getEntityId(), properties);
        }
        log.info("Done adding associations for entity.");
    }

    public void deleteEntityAssociations(Associable associable) {
        log.info("Removed all associations for entity.");
        associable.removeAllAssociables();
        log.info("Done removing associations for entity.");

    }

    public <T extends IEntityRequest> void removeAssociations(Associable associable, T request) {
        for (EntityAssociation assoc : request.getAssociations()) {
            associable.removeBiDirectionalAssociable(assoc.getEntityType(), assoc.getEntityId());
        }
    }

    public List<DocumentDto> addFiles(Associable associable, Map<String, MultipartFile> files) {
        return documentService.upload(associable, files);
    }
}