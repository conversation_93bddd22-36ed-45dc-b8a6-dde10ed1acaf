package com.scube.licensing.features.participant.dog;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandHandler;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class DogHandler {
    private final DogService dogService;

    @QueryHandler
    public List<Participant> getAssociatedDogs(GetAssociatedDogsQuery query) {
        return dogService.getAssociatedDogs(query.profile());
    }

    @Transactional
    @CommandHandler
    public void changeAllLicenseStatusesForADog(UpdateDogsLicenseStatusCommand command) {
        dogService.changeAllLicenseStatusesForADog(command.dog(), command.status(), command.modifier());
    }

    public record GetAssociatedDogsQuery(Associable profile) implements IRequestAxon<List<Participant>> {}
    public record UpdateDogsLicenseStatusCommand(Participant dog, String status, String modifier) implements IRequestVoidAxon {}
}
