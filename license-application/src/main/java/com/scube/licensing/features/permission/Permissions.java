package com.scube.licensing.features.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2025-01-24T22:44:10.546492600Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.547492500Z
     */
    public static class LoggedInUserMergeByCode {
        public static final String MERGE_BY_CODE = "license-service-me-merge-by-code-merge-by-code";

        public static final String EXISTS_BY_REGISTRATION_CODE = "license-service-me-merge-by-code-exists-by-registration-code";

        private LoggedInUserMergeByCode() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.559615300Z
     */
    public static class Participant {
        public static final String MARK_APPROVED = "license-service-participant-mark-approved";

        public static final String MARK_UNAPPROVED = "license-service-participant-mark-unapproved";

        public static final String MARK_ONLINE_RESIDENT = "license-service-participant-mark-online-resident";

        public static final String MARK_OFFLINE_RESIDENT = "license-service-participant-mark-offline-resident";

        public static final String CREATE_RESIDENT = "license-service-participant-create-resident";

        public static final String PATCH_PARTICIPANT_CONTACT = "license-service-participant-patch-participant-contact";

        public static final String UPDATE_PARTICIPANT = "license-service-participant-update-participant";

        public static final String DELETE_PARTICIPANT = "license-service-participant-delete-participant";
        public static final String QUERY = "license-service-participant-query";

        private Participant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.559615300Z
     */
    public static class LoggedInUserLicense {
        public static final String CREATE_LICENSE = "license-service-me-license-create-license";

        public static final String CREATE_PENDING_LICENSE = "license-service-me-license-create-pending-license";

        public static final String CREATE_FINAL_LICENSE = "license-service-me-license-create-final-license";

        public static final String ADD_DOG_TO_LICENSE = "license-service-me-license-add-dog-to-license";

        public static final String RENEW_LICENSE = "license-service-me-license-renew-license";

        public static final String DELETE_LICENSE_DRAFT = "license-service-me-license-delete-license-draft";

        public static final String GET_LICENSE_FEES = "license-service-me-license-get-license-fees";

        public static final String CALCULATE_LICENSE_FEES = "license-service-me-license-calculate-license-fees";

        public static final String MARK_LICENSE_AS_PENDING_APPROVAL = "license-service-me-license-mark-license-as-pending-approval";

        public static final String GET_LICENSE_FORM = "license-service-me-license-get-license-form";

        private LoggedInUserLicense() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.560621300Z
     */
    public static class PublicProfile {
        public static final String GET_DOG = "license-service-public-profile-get-dog";

        private PublicProfile() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.560621300Z
     */
    public static class Profile {
        public static final String GET_INDIVIDUAL_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-individual-entity-and-all-associations";

        public static final String GET_LICENSE_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-license-entity-and-all-associations";

        public static final String GET_DOG_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-dog-entity-and-all-associations";

        public static final String GET_ADDRESS_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-address-entity-and-all-associations";

        public static final String GET_DOCUMENT_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-document-entity-and-all-associations";

        public static final String GET_BUSINESS_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-profile-get-business-entity-and-all-associations";

        public static final String ADD_TO_REJECTED_FIELD_LIST = "license-service-profile-add-to-rejected-field-list";

        public static final String REMOVE_FROM_REJECTED_FIELD_LIST = "license-service-profile-remove-from-rejected-field-list";

        public static final String CLEAR_REJECTED_FIELD_LIST = "license-service-profile-clear-rejected-field-list";

        public static final String GET_REJECTED_FIELD_LIST = "license-service-profile-get-rejected-field-list";

        private Profile() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.560621300Z
     */
    public static class LicenseStatus {
        public static final String CREATE_LICENSE_STATUS = "license-service-config-license-status-create-license-status";

        public static final String GET_ALL_LICENSE_STATUS = "license-service-config-license-status-get-all-license-status";

        public static final String GET_LICENSE_STATUS_BY_ID = "license-service-config-license-status-get-license-status-by-id";

        public static final String UPDATE_LICENSE_STATUS = "license-service-config-license-status-update-license-status";

        public static final String DELETE_LICENSE_STATUS_BY_ID = "license-service-config-license-status-delete-license-status-by-id";

        private LicenseStatus() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.560621300Z
     */
    public static class Document {
        public static final String FILE_UPLOAD = "license-service-document-file-upload";

        public static final String GET_DOCUMENTS = "license-service-document-get-documents";

        public static final String GET_DOCUMENT = "license-service-document-get-document";

        public static final String GET_DOCUMENT_HISTORY = "license-service-document-get-document-history";

        public static final String DELETE = "license-service-document-delete";

        public static final String GET_DOCUMENT_TYPE = "license-service-document-get-document-type";

        public static final String GET_DOCUMENT_TYPES = "license-service-document-get-document-types";

        public static final String SAVE_DOCUMENT_TYPE = "license-service-document-save-document-type";

        private Document() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.561621400Z
     */
    public static class License {
        public static final String CREATE_LICENSE = "license-service-license-create-license";

        public static final String CREATE_PENDING_LICENSE = "license-service-license-create-pending-license";

        public static final String CREATE_FINAL_LICENSE = "license-service-license-create-final-license";

        public static final String GET_OUTSTANDING_APPROVALS = "license-service-license-get-outstanding-approvals";

        public static final String MARK_LICENSE_AS_APPROVED = "license-service-license-mark-license-as-approved";

        public static final String MARK_LICENSE_AS_PENDING_APPROVAL = "license-service-license-mark-license-as-pending-approval";

        public static final String MARK_LICENSE_AS_DENIED = "license-service-license-mark-license-as-denied";

        public static final String CHANGE_LICENSE_STATUS = "license-service-license-change-license-status";

        public static final String ADD_DOG_TO_LICENSE = "license-service-license-add-dog-to-license";

        public static final String REMOVE_PARTICIPANT = "license-service-license-remove-participant";

        public static final String GET_LICENSE_FEES = "license-service-license-get-license-fees";

        public static final String CALCULATE_LICENSE_FEES = "license-service-license-calculate-license-fees";

        public static final String RENEW_LICENSE = "license-service-license-renew-license";

        public static final String GET_LICENSE_ACTIONS = "license-service-license-get-license-actions";

        public static final String IS_VACCINATION_EXPIRED = "license-service-license-is-vaccination-expired";

        public static final String DELETE_LICENSE_DRAFT = "license-service-license-delete-license-draft";

        public static final String RE_PROCESS_FEE = "license-service-license-re-process-fee";

        public static final String QUERY = "license-service-license-query";

        public static final String GET_LICENSE_FORM = "license-service-license-get-license-form";

        private License() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.561621400Z
     */
    public static class Report {
        public static final String GET_DOG_LICENSE_RENEWAL_NOTICE = "license-service-report-get-dog-license-renewal-notice";

        public static final String GET_DOG_LICENSE_ONLINE_ANNOUNCEMENT_LETTER = "license-service-report-get-dog-license-online-announcement-letter";

        public static final String GET_ADHOC_REPORT = "license-service-report-get-adhoc-report";

        private Report() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.561621400Z
     */
    public static class LicenseReminder {
        public static final String RUN_DELINQUENT_REMINDERS = "license-service-reminders-run-delinquent-reminders";

        public static final String RUN_EXPIRATION_REMINDERS = "license-service-reminders-run-expiration-reminders";

        public static final String RUN_EXPIRE_LICENSES = "license-service-reminders-run-expire-licenses";

        private LicenseReminder() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.561621400Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "license-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "license-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.561621400Z
     */
    public static class EventType {
        public static final String GET_EVENT_TYPE = "license-service-event-type-get-event-type";

        public static final String GET_EVENT_TYPE_FOR_DROP_DOWN = "license-service-event-type-get-event-type-for-drop-down";

        public static final String GET_EVENT_TYPES = "license-service-event-type-get-event-types";

        public static final String GET_EVENT_TYPES_BY_PROFILE_TYPE = "license-service-event-type-get-event-types-by-profile-type";

        public static final String CREATE_EVENT_TYPE = "license-service-event-type-create-event-type";

        private EventType() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.562621300Z
     */
    public static class LoggedInUserEntityFee {
        public static final String GET_FEES = "license-service-me-entity-fees-get-fees";

        public static final String GET_FEES_1 = "license-service-me-entity-fees-get-fees";

        public static final String GET_FEE = "license-service-me-entity-fees-get-fee";
        public static final String GET_UNPAID_FEES = "license-service-me-entity-fees-get-unpaid-fees";

        private LoggedInUserEntityFee() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.562621300Z
     */
    public static class EntityGroup {
        public static final String CREATE_ENTITY_GROUP = "license-service-entity-group-create-entity-group";

        public static final String ADD_FILE = "license-service-entity-group-add-file";

        public static final String ADD_ASSOCIATION = "license-service-entity-group-add-association";

        public static final String UPDATE_ENTITY_GROUP = "license-service-entity-group-update-entity-group";

        public static final String GET_ALL_GROUPS = "license-service-entity-group-get-all-groups";

        public static final String GET_GROUP = "license-service-entity-group-get-group";

        public static final String DELETE_GROUP = "license-service-entity-group-delete-group";

        private EntityGroup() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.562621300Z
     */
    public static class MergeRequest {
        public static final String CREATE_MERGE_REQUEST = "license-service-merge-requests-create-merge-request";

        public static final String GET_MERGE_REQUESTS = "license-service-merge-requests-get-merge-requests";

        public static final String GET_MERGE_REQUESTS_BY_REQUEST_USER = "license-service-merge-requests-get-merge-requests-by-request-user";

        public static final String APPROVE_MERGE_REQUEST = "license-service-merge-requests-approve-merge-request";

        public static final String REJECT_MERGE_REQUEST = "license-service-merge-requests-reject-merge-request";

        private MergeRequest() {
        }
    }

    public static class Migration {
        public static final String MIGRATE_CODE_LOOKUP = "license-service-migration-migrate-code-lookup";
        public static final String MIGRATE_CODE_LOOKUP_ALL_TENANT = "license-service-migration-migrate-code-lookup-all-tenant";

        private Migration() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.562621300Z
     */
    public static class LoggedInUserProfile {
        public static final String GET_INDIVIDUAL_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-me-profile-get-individual-entity-and-all-associations";

        public static final String GET_INDIVIDUAL_ENTITY_AND_ALL_ASSOCIATIONS_1 = "license-service-me-profile-get-individual-entity-and-all-associations";

        public static final String ADD_TO_REJECTED_FIELD_LIST = "license-service-me-profile-add-to-rejected-field_list";

        public static final String GET_LICENSE_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-me-profile-get-license-entity-and-all-associations";

        public static final String GET_DOG_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-me-profile-get-dog-entity-and-all-associations";

        public static final String GET_ADDRESS_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-me-profile-get-address-entity-and-all-associations";

        public static final String GET_DOCUMENT_ENTITY_AND_ALL_ASSOCIATIONS = "license-service-me-profile-get-document-entity-and-all-associations";

        public static final String GET_REJECTED_FIELD_LIST = "license-service-me-profile-get-rejected-field-list";

        public static final String REMOVE_FROM_REJECTED_FIELD_LIST = "license-service-me-profile-remove-from-rejected-field-list";

        public static final String CLEAR_REJECTED_FIELD_LIST = "license-service-me-profile-clear-rejected-field-list";

        private LoggedInUserProfile() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class QrCode {
        public static final String GENERATE_AND_UPLOAD = "license-service-qr-codes-generate-and-upload";

        public static final String GENERATE_QR_CODE_NO_UPLOAD = "license-service-qr-codes-generate-qr-code-no-upload";

        public static final String GENERATE_BY_CODE = "license-service-qr-codes-generate-by-code";

        public static final String GENERATE_WITH_TEXT = "license-service-qr-codes-generate-with-text";

        public static final String GENERATE_BATCH = "license-service-qr-codes-generate-batch";

        private QrCode() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class LoggedInUserEvent {
        public static final String ADD_INDIVIDUAL_EVENT = "license-service-me-event-add-individual-event";

        public static final String ADD_DOG_EVENT = "license-service-me-event-add-dog-event";

        public static final String ADD_LICENSE_EVENT = "license-service-me-event-add-license-event";

        public static final String ADD_ADDRESS_EVENT = "license-service-me-event-add-address-event";

        public static final String ADD_DOCUMENT_EVENT = "license-service-me-event-add-document-event";

        private LoggedInUserEvent() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class AppProperties {
        public static final String GET_ALL_PROPERTIES = "license-service-app-properties-get-all-properties";

        public static final String GET_CACHE = "license-service-app-properties-get-cache";

        public static final String GET_PROPERTY_BY_UUID = "license-service-app-properties-get-property-by-uuid";

        public static final String GET_PROPERTY_BY_NAME = "license-service-app-properties-get-property-by-name";

        public static final String UPDATE_PROPERTY = "license-service-app-properties-update-property";

        public static final String DELETE_PROPERTY_BY_UUID = "license-service-app-properties-delete-property-by-uuid";

        private AppProperties() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class CodeLookup {
        public static final String GET_BY_ENTITY_TYPE_AND_ENTITY_ID = "license-service-code-lookup-get-by-entity-type-and-entity-id";

        public static final String CREATE_OR_LOOKUP = "license-service-code-lookup-create-or-lookup";

        public static final String CREATE = "license-service-code-lookup-create";

        public static final String BATCH_TAG_CREATE = "license-service-code-lookup-batch-tag-create";
        public static final String TAG_UPDATE = "license-service-code-lookup-tag-update";

        private CodeLookup() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class Entity {
        public static final String CREATE_ENTITY = "license-service-entity-create-entity";

        public static final String UPDATE_ENTITY = "license-service-entity-update-entity";

        public static final String DELETE_ENTITY = "license-service-entity-delete-entity";

        public static final String ADD_ASSOCIATION = "license-service-entity-add-association";

        public static final String REMOVE_ASSOCIATION = "license-service-entity-remove-association";

        private Entity() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class PublicCodeLookup {
        public static final String GET_BY_CODE = "license-service-public-code-lookup-get-by-code";

        private PublicCodeLookup() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.563621800Z
     */
    public static class PublicDog {
        public static final String REPORT_SIGHTING = "license-service-public-dog-report-sighting";

        private PublicDog() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class Event {
        public static final String ADD_INDIVIDUAL_EVENT = "license-service-event-add-individual-event";

        public static final String ADD_DOG_EVENT = "license-service-event-add-dog-event";

        public static final String ADD_LICENSE_EVENT = "license-service-event-add-license-event";

        public static final String ADD_ADDRESS_EVENT = "license-service-event-add-address-event";

        public static final String ADD_DOCUMENT_EVENT = "license-service-event-add-document-event";

        private Event() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class Search {
        public static final String SEARCH_INDIVIDUAL = "license-service-search-search-individual";

        public static final String SEARCH_ORGANIZATION = "license-service-search-search-organization";

        public static final String SEARCH_PARCEL = "license-service-search-search-parcel";

        public static final String SEARCH_DOG = "license-service-search-search-dog";

        public static final String SEARCH_GLOBAL = "license-service-search-search-global";

        public static final String SEARCH_EMAIL_EXISTS = "license-service-search-search-email-exists";

        private Search() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class MergeByCode {
        public static final String MERGE_BY_CODE = "license-service-merge-by-code-merge-by-code";

        public static final String EXISTS_BY_REGISTRATION_CODE = "license-service-merge-by-code-exists-by-registration-code";

        private MergeByCode() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class LoggedInUserMergeRequest {
        public static final String CREATE_MERGE_REQUEST = "license-service-me-merge-requests-create-merge-request";

        public static final String GET_MERGE_REQUESTS = "license-service-me-merge-requests-get-merge-requests";

        public static final String GET_MERGE_REQUEST = "license-service-me-merge-requests-get-merge-request";

        private LoggedInUserMergeRequest() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class LoggedInUserEventType {
        public static final String GET_EVENT_TYPE_FOR_DROP_DOWN = "license-service-me-event-type-get-event-type-for-drop-down";

        public static final String GET_EVENT_TYPES = "license-service-me-event-type-get-event-types";

        private LoggedInUserEventType() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class EntityFee {
        public static final String ADD_FEE = "license-service-entity-fee-add-fee";

        public static final String ADD_FILE = "license-service-entity-fee-add-file";

        public static final String ADD_ASSOCIATION = "license-service-entity-fee-add-association";

        public static final String UPDATE_FEE = "license-service-entity-fee-update-fee";

        public static final String GET_FEES = "license-service-entity-fee-get-fees";

        public static final String GET_FEE = "license-service-entity-fee-get-fee";

        public static final String DELETE_FEE = "license-service-entity-fee-delete-fee";
        public static final String RESUME_RECURRING_FEE = "license-service-entity-fee-resume-recurring-fee";
        public static final String PAUSE_RECURRING_FEE = "license-service-entity-fee-pause-recurring-fee";
        public static final String GET_UNPAID_FEES = "license-service-entity-fee-get-unpaid-fees";

        private EntityFee() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.564623100Z
     */
    public static class LicenseFees {
        public static final String UPDATE_LICENSE_FEE = "license-service-fees-update-license-fee";

        public static final String ADD_LICENSE_FEE = "license-service-fees-add-license-fee";

        public static final String REMOVE_LICENSE_FEE = "license-service-fees-remove-license-fee";

        private LicenseFees() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.565622500Z
     */
    public static class LicenseType {
        public static final String CREATE_LICENSE_TYPE = "license-service-config-license-type-create-license-type";

        public static final String GET_ALL_LICENSE_TYPES = "license-service-config-license-type-get-all-license-types";

        public static final String GET_LICENSE_TYPE_BY_ID = "license-service-config-license-type-get-license-type-by-id";

        public static final String UPDATE_LICENSE_TYPE = "license-service-config-license-type-update-license-type";

        public static final String DELETE_LICENSE_TYPE = "license-service-config-license-type-delete-license-type";

        private LicenseType() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-01-24T22:44:10.565622500Z
     */
    public static class LoggedInUserParticipant {
        public static final String CREATE_ONLINE_RESIDENT = "license-service-me-participant-create-online-resident";

        public static final String MARK_ONLINE_RESIDENT = "license-service-me-participant-mark-online-resident";

        public static final String MARK_OFFLINE_RESIDENT = "license-service-me-participant-mark-offline-resident";

        public static final String UPDATE_ONLINE_PARTICIPANT_CONTACT = "license-service-me-participant-update-online-participant-contact";

        public static final String UPDATE_INDIVIDUAL = "license-service-me-participant-update-individual";

        public static final String UPDATE_DOG = "license-service-me-participant-update-dog";

        public static final String GET_PARTICIPANT = "license-service-me-participant-get-participant";

        private LoggedInUserParticipant() {
        }
    }
}
