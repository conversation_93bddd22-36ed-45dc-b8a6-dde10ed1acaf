<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="jira" id="lem-01">
        <sql splitStatements="false">
            update custom_field_value cfv
            set table_custom_field_id = tcf.table_custom_field_id
            from table_custom_field tcf
            where tcf.name = 'licenseExempt'
            and tcf.table_type_id = 34
            and cfv.table_custom_field_id = (
            select table_custom_field_id from table_custom_field
            where name = 'licenseExemptionDocumentsProvided'
            );
        </sql>
    </changeSet>
    <changeSet author="jira" id="lem-02">
        <sql splitStatements="false">
            update custom_field_value
            set value_type = 'boolean', boolean_value = (case when coalesce(int_value, 0) = 1 then true else false end),
            int_value = null
            where table_custom_field_id in (
            select table_custom_field_id from table_custom_field
            where name = 'licenseExempt'
            );
        </sql>
    </changeSet>
</databaseChangeLog>