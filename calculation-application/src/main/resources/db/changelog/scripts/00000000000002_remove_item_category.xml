<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="<PERSON>" id="1">
        <dropColumn tableName="cart_item" columnName="category_id"/>
    </changeSet>
    <changeSet author="Peter Wood" id="2">
        <dropTable tableName="itemcategory"/>
    </changeSet>
    <changeSet author="Peter Wood" id="3">
        <dropColumn tableName="fee" columnName="included"/>
    </changeSet>
    <changeSet author="Peter Wood" id="4">
        <dropUniqueConstraint tableName="cart_item_fees" constraintName="uk_1acdmj7v388kuuxpreddij09i" />
    </changeSet>
    <changeSet author="Peter Wood" id="6">
        <addUniqueConstraint columnNames="key" tableName="fee" />
    </changeSet>
    <changeSet id="Peter Wood" author="5">
        <addNotNullConstraint tableName="fee" columnName="amount" />
    </changeSet>
    <changeSet id="Peter Wood" author="7">
        <renameColumn tableName="cart_item" oldColumnName="item_id" newColumnName="unique_item_id" />
        <renameColumn tableName="cart_item" oldColumnName="item_identifier" newColumnName="item_type_id" />
    </changeSet>
</databaseChangeLog>
