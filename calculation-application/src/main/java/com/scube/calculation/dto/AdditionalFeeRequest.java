package com.scube.calculation.dto;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalFeeRequest {
    private List<String> feeCode;
    private String label;
    private BigDecimal minFee;
    private JsonNode feeJson;
}
