package com.scube.config.json_storage.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.config.json_storage.JsonStorageService;
import com.scube.config.permission.Permissions;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/me/json-storage")
@Validated
@GenerateHttpExchange(ServiceUrlConstant.CONFIG_SERVICE)
@RequiredArgsConstructor
public class LoggedInUserJsonStorageController {
    private final JsonStorageService jsonStorageService;

    @GetMapping
    @RolesAllowed(Permissions.LoggedInUserJsonStorage.GET_JSON_STORAGE)
    @Parameter(
            name = "fields",
            description = "Fields to filter by",
            schema = @Schema(type = "object")
    )
    public ResponseEntity<JsonNode> getJsonStorage(@RequestParam(required = false) Long id,
                                                   @RequestParam(required = false) UUID uuid,
                                                   @RequestParam(required = false) Map<String, Object> fields) {
        return jsonStorageService.get(id, uuid, fields);
    }
}