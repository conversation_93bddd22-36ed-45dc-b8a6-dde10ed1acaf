package com.scube.report.features.licensing.dog.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Map;

import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Documented
@Target(PARAMETER)
@Retention(RUNTIME)
@Constraint(validatedBy = { ValidReportRequest.ValidReportRequestValidator.class })
public @interface ValidReportRequest {
    String message() default "{com.scube.coordinator.features.reports.validation.ValidReportRequest.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


    class ValidReportRequestValidator
            implements ConstraintValidator<ValidReportRequest, Map<String, String>> {

        public void initialize(ValidReportRequest constraintAnnotation) {

        }

        @Override
        public boolean isValid(Map<String, String> request,
                               ConstraintValidatorContext context) {

            if (isBlank(request.get("startDate"))) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("startDate must not be empty")
                        .addConstraintViolation();
                return false;
            } else if (isBlank(request.get("endDate"))) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("endDate must not be empty")
                        .addConstraintViolation();
                return false;
            }

            return true;
        }
    }
}
