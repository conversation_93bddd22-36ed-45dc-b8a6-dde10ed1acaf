package com.scube.auth.controllers;

import com.scube.auth.permission.Permissions;
import com.scube.auth.services.RealmService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.keycloak.representations.idm.RealmRepresentation;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/realm")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.AUTH_SERVICE)
@Validated
public class RealmController {
    private final RealmService realmService;

    @GetMapping("/{realmName}/representation")
    @Operation(summary = "Get realm by name", description = "Returns realm with its identity providers")
    @ApiResponse(responseCode = "200", description = "Realm found")
    @RolesAllowed(Permissions.Realm.GET_REALM_REPRESENTATION)
    public RealmRepresentation getRealmRepresentation(@PathVariable @Size(max = 255) String realmName) {
        return realmService.getRealmRepresentation(realmName);
    }

    @PostMapping
    @Operation(summary = "Create new realm", description = "Creates a new realm. You can use the Json from an existing realm as a template")
    @ApiResponse(responseCode = "201", description = "Realm created")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Realm.CREATE_REALM)
    public void createRealm(@RequestBody RealmRepresentation realmRepresentation) {
        realmService.createRealm(realmRepresentation);
    }

    @PostMapping("/{realmName}/create-role")
    @Operation(summary = "Add role to realm", description = "Add role to realm")
    @ApiResponse(responseCode = "200", description = "Role added")
    @RolesAllowed(Permissions.Realm.ADD_ROLE_TO_REALM)
    public void addRoleToRealm(@PathVariable @Size(max = 255) String realmName, @RequestParam @Size(max = 255) String roleName) {
        realmService.addRoleToRealm(realmName, roleName);
    }

    @GetMapping("/{realmName}/create-composite-role")
    @Operation(summary = "Create composite role", description = "Create composite role")
    @ApiResponse(responseCode = "200", description = "Composite role created")
    @RolesAllowed(Permissions.Realm.CREATE_COMPOSITE_ROLE)
    public void createCompositeRole(@PathVariable @Size(max = 255) String realmName, @RequestBody CompositeRoleRequest request) {
        realmService.addCompositeRolesToRole(realmName, request.roleName(), request.compositeRoleNames());
    }

    // @formatter:off
    public record CompositeRoleRequest(String roleName, List<String> compositeRoleNames) {}
}