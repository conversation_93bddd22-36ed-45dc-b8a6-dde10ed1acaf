<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711562080283-52">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique"
                             tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-53">
        <addColumn tableName="app_properties">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-54">
        <addColumn tableName="audit_log_app_properties">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-55">
        <addColumn tableName="audit_log_notification">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-56">
        <addColumn tableName="audit_log_notification_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-57">
        <addColumn tableName="notification">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-58">
        <addColumn tableName="notification_status">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-59">
        <addColumn tableName="audit_log_app_properties">
            <column defaultValueComputed="gen_random_uuid()" name="app_properties_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-60">
        <addColumn tableName="audit_log_notification_status">
            <column defaultValueComputed="gen_random_uuid()" name="notification_status_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-61">
        <addColumn tableName="audit_log_notification">
            <column defaultValueComputed="gen_random_uuid()" name="notification_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-62">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_scheduler"
                                 constraintName="fkggi0jubkjahhqj63qb2hyn8kb" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-63">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type"
                                 constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-64">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key"
                              tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-65">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-66">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-1">
        <modifyDataType columnName="app_properties_uuid" newDataType="uuid" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-2">
        <addDefaultValue columnDataType="uuid" columnName="app_properties_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-3">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-4">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-5">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-6">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-7">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-8">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-9">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-10">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-11">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-12">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-13">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler"
                              validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-14">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-15">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-16">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-17">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-18">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-19">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-20">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-21">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-22">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-23">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-24">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-25">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-26">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-27">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-28">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-29">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-30">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler"
                              validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-31">
        <modifyDataType columnName="notification_status_uuid" newDataType="uuid" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-32">
        <addDefaultValue columnDataType="uuid" columnName="notification_status_uuid"
                         defaultValueComputed="gen_random_uuid()" tableName="notification_status"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-33">
        <modifyDataType columnName="notification_uuid" newDataType="uuid" tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-34">
        <addDefaultValue columnDataType="uuid" columnName="notification_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="notification"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-35">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-36">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-37">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-38">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-39">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-40">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-41">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-42">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-43">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-44">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-45">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()"
                         tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-46">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-47">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-48">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-49">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name"
                               tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-50">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-51">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <!-- modify Notification correlationId to UUID -->
    <changeSet author="postgres (generated)" id="1711562080283-67">
        <sql>
            Alter table notification
            Alter column correlation_id type uuid using correlation_id::uuid;
        </sql>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711562080283-68">
        <sql>
            Alter table audit_log_notification
            Alter column correlation_id type uuid using correlation_id::uuid;
        </sql>
    </changeSet>
</databaseChangeLog>
