package com.scube.notification.features.notification.rabbit.listener;

import com.scube.notification.features.notification.rabbit.events.ScheduleEmailFanoutEvent;
import com.scube.notification.features.notification.service.NotificationMapperService;
import com.scube.notification.features.notification.service.NotificationService;
import com.scube.notification.features.notification.validation.service.ValidationService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component
public class EmailEventFanoutListener extends FanoutListener<ScheduleEmailFanoutEvent> {

    private final NotificationService notificationService;
    private final NotificationMapperService notificationMapperService;
    private final ValidationService validationService;

    @Override
    public void consume(ScheduleEmailFanoutEvent event) {
        log.debug("EmailCommandListener.consume()");

        try {
            validationService.validate(event);
            notificationService.create(notificationMapperService.of(event));
        } catch (Throwable t) {
            log.error("Error while creating notification", t);
        }
    }
}


