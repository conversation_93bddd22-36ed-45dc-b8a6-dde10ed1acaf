package com.scube.notification.features.notification.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.notification.db.entity.NotificationType;
import com.scube.notification.features.notification.dto.NotificationCreateRequest;
import com.scube.notification.features.notification.rabbit.events.ScheduleEmailFanoutEvent;
import com.scube.notification.features.notification.rabbit.events.ScheduleSMSFanoutEvent;
import com.scube.notification.features.processing.email.model.Email;
import com.scube.notification.features.processing.sms.model.SMS;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@AllArgsConstructor
@Service
@Slf4j
public class NotificationMapperService {
    /**
     * Maps a {@link ScheduleEmailFanoutEvent} to a {@link NotificationCreateRequest} with details pertaining to email notifications.
     *
     * @param event The email scheduling event.
     * @return A {@link NotificationCreateRequest} containing details from the given email event.
     */
    public NotificationCreateRequest of(ScheduleEmailFanoutEvent event) {
        log.debug("NotificationMapperService.of()");

        ObjectMapper objectMapper = new ObjectMapper();

        ObjectNode jsonNode = objectMapper.createObjectNode();

        Email email = event.getEmail();

        jsonNode.put("to", ignoreNull(email.getTo()));
        jsonNode.put("from", ignoreNull(email.getFrom()));
        jsonNode.put("cc", ignoreNull(email.getCc()));
        jsonNode.put("bcc", ignoreNull(email.getBcc()));
        jsonNode.put("body", ignoreNull(email.getBody()));
        jsonNode.put("subject", ignoreNull(email.getSubject()));
        jsonNode.put("contentType", ignoreNull(email.getContentType()));

        ArrayNode attachmentUuids = jsonNode.putArray("attachmentUuids");

        if (email.getAttachmentUuids() != null) {
            for (String attachmentUuid : email.getAttachmentUuids()) {
                attachmentUuids.add(attachmentUuid);
            }
        }

        return NotificationCreateRequest.builder()
                .notificationType(NotificationType.EMAIL)
                .topic(event.getTopic())
                .tag(event.getTag())
                .correlationId(event.getCorrelationId())
                .notification(jsonNode)
                .executionTs(event.getExecutionTs())
                .createdBy(event.getCreatedBy())
                .build();
    }

    private String ignoreNull(String value) {
        return Optional.ofNullable(value).orElse("");
    }

    /**
     * Maps a {@link ScheduleSMSFanoutEvent} to a {@link NotificationCreateRequest} with details pertaining to SMS notifications.
     *
     * @param event The SMS scheduling event.
     * @return A {@link NotificationCreateRequest} containing details from the given SMS event.
     */
    public NotificationCreateRequest of(ScheduleSMSFanoutEvent event) {
        log.debug("NotificationMapperService.of()");

        ObjectMapper objectMapper = new ObjectMapper();

        ObjectNode jsonNode = objectMapper.createObjectNode();

        SMS sms = event.getSms();

        jsonNode.put("toNumber", sms.getToNumber());
        jsonNode.put("fromNumber", sms.getFromNumber());
        jsonNode.put("message", sms.getMessage());

        return NotificationCreateRequest.builder()
                .notificationType(NotificationType.SMS)
                .correlationId(event.getCorrelationId())
                .tag(event.getTag())
                .topic(event.getTopic())
                .notification(jsonNode)
                .executionTs(event.getExecutionTs())
                .createdBy(event.getCreatedBy())
                .build();
    }
}
