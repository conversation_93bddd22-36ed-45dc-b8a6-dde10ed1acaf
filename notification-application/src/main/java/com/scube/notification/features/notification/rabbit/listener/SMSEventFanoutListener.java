package com.scube.notification.features.notification.rabbit.listener;

import com.scube.notification.features.notification.rabbit.events.ScheduleSMSFanoutEvent;
import com.scube.notification.features.notification.service.NotificationMapperService;
import com.scube.notification.features.notification.service.NotificationService;
import com.scube.notification.features.notification.validation.service.ValidationService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component
public class SMSEventFanoutListener extends FanoutListener<ScheduleSMSFanoutEvent> {
    private final NotificationService notificationService;
    private final NotificationMapperService notificationMapperService;
    private final ValidationService validationService;

    @Override
    public void consume(ScheduleSMSFanoutEvent event) {
        log.debug("SMSCommandListener.consume()");

        try {
            validationService.validate(event);
            notificationService.create(notificationMapperService.of(event));
        } catch (Throwable t) {
            log.error("Error while creating notification", t);
        }
    }


}

