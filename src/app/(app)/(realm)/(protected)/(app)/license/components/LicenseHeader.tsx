import { Individual } from "@/types/IndividualType";
import React from "react";
import { entityDisplay } from "../../entity/[entitytype]/[entityId]/profile/entityHelper";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { BiEnvelope, BiPhone } from "react-icons/bi";

type LicenseHeaderProps = {
  holder: Individual;
  entityId: string;
  entityType: string;
};

const LicenseHeader = ({
  holder,
  entityId,
  entityType,
}: LicenseHeaderProps) => {
  const ent = entityDisplay(holder, entityType);
  console.log(ent);

  const licenseIcon = "/images/icons/license.png";

  return (
    <div
      className={`
      flex flex-col items-center shrink-0 relative pt-6 px-6 bg-white
    `}
    >
      <div className="flex gap-6 items-center shrink-0 w-full ">
        <div
          className="
          flex flex-col justify-between container mx-auto w-full gap-0 pt-0 items-center text-center mb-10
          md:flex-row md:gap-8 md:items-start md:pt-8 md:mb-0 md:text-left
        "
        >
          <div
            className="
            flex flex-col items-center justify-start w-full gap-6
            md:flex-row md:gap-8 md:items-start
          "
          >
            <Image
              src={ent?.avatarUrl || licenseIcon}
              alt="avatar"
              width={120}
              height={120}
              className={`rounded-full border-2 shadow-xl shadow-neutral-400 transform translate-y-[-20%] 
              ${ent?.active ? "" : "grayscale"}
              `}
            />

            <div className="flex flex-col h-full ">
              <div className="flex flex-row items-center justify-start gap-2 ">
                <p className="font-bold text-2xl ">
                  {ent?.primaryDisplay || ""}
                </p>
                <div className="">
                  <Badge variant={ent?.active ? "success" : "destructive"}>
                    {ent?.active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-neutral-500 italic mb-1">
                {ent?.secondaryDisplay ?? ""}
              </p>
              <p className="">{ent?.thirdDisplay ?? ""}</p>
              {ent?.contacts && (
                <div className="flex flex-wrap gap-4 items-center">
                  <p className="text-sm text-neutral-600 flex items-center">
                    <BiPhone className="inline-block mr-1" />
                    {ent?.contacts?.phone}
                  </p>
                  <p className="text-sm text-neutral-600 flex items-center">
                    <BiEnvelope className="inline-block mr-1" />
                    {ent?.contacts?.email}
                  </p>
                </div>
              )}
              <p className="text-red-600 text-sm">
                {ent?.warningDisplay ?? ""}
              </p>
            </div>
          </div>

          {/* Events Dropdown */}
          {/* <div className="mt-4 h-full">
            <ProfileActions
              entityId={entityId}
              entityType={entityType}
              className="flex items-center justify-center gap-2 text-white py-2 px-4 rounded-md bg-clerk-background transition-all hover:bg-clerk-primary"
            >
              Actions <FiChevronDown />
            </ProfileActions>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default LicenseHeader;
