"use client";

import { useState } from "react";
import CardN<PERSON>ber from "./CardNumber";
import type { CardType } from "@/types/CreditCardType";
import Image from "next/image";
import CardExpiration from "./CardExpiration";

const CreditCard = ({
  setCardInfo,
  cardInfo,
}: {
  setCardInfo: any;
  cardInfo: any;
}) => {
  const [cardNumber, setCardNumber] = useState<string>("");
  const [cardType, setCardType] = useState<CardType>(null);

  const [cardName, setCardName] = useState<string>("");
  const [cardExpiration, setCardExpiration] = useState("");
  const [cardCVC, setCardCVC] = useState<string>("");

  const inputStyle = "bg-gray-100 rounded-md p-2";

  const handleCardCVC = (input: string) => {
    let value = input.replace(/\D/g, "");
    setCardCVC(value);
  };

  return (
    <div className="flex w-full flex-col gap-6 py-6">
      {/* Credit Card Number */}
      <CardNumber
        cardNumber={cardNumber}
        setCardNumber={setCardNumber}
        cardType={cardType}
        setCardType={setCardType}
      />

      {/* Name on Card */}
      <div className="flex flex-col">
        <label htmlFor="cardName">Name on card</label>
        <input
          type="text"
          name="cardName"
          placeholder="Jane Doe"
          id="cardName"
          className={`${inputStyle} `}
          value={cardName}
          onChange={(e) => {
            setCardName(e.target.value);
          }}
        />
      </div>
      <div className="flex w-full gap-6">
        {/* Card Expiration */}
        <CardExpiration
          cardExpiration={cardExpiration}
          setCardExpiration={setCardExpiration}
        />

        {/* Card CVC */}
        <div className="relative flex w-1/2 flex-col">
          <label htmlFor="cardCVC">Card CVC</label>
          <input
            type="tel"
            name="cardCVC"
            id="cardCVC"
            className={`${inputStyle} relative`}
            onChange={(e) => {
              handleCardCVC(e.target.value);
            }}
            maxLength={cardType === "amex" ? 4 : 3}
            placeholder="123"
          />
          <Image
            src="/images/cardicons/backcard.svg"
            className="absolute bottom-3 right-3"
            width={28}
            height={28}
            alt="back of card"
          />
        </div>
      </div>
      {/* <div>
        Card: {cardNumber.replace(/\D/g, '')}<br/>
        Name: {cardName}<br/>
        Expiry: {cardExpiration}<br/>
        CVV: {cardCVC}
      </div> */}
    </div>
  );
};

export default CreditCard;
