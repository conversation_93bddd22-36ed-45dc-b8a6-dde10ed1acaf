import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "../button";
import { DropdownMenuGroup } from "@radix-ui/react-dropdown-menu";
import { useEffect } from "react";
import { useAtom } from "jotai";
import { menuAtom } from "@/atoms/dropdownAtom";

type MenuItem = {
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  shortcut?: string;
};

type MenuGroupProps = {
  title?: string;
  items: MenuItem[];
};

export type MenuProps = {
  title: string;
  contents: {
    title?: string;
    items: (MenuGroupProps | MenuItem)[];
  };
};

const Menu = () => {
  const [menu] = useAtom(menuAtom);

  const { title, contents } = (menu as MenuProps) ?? {};

  console.log("menu: ", menu);

  // Determine if the contents are a group or a single item
  function isGroup(item: MenuGroupProps | MenuItem): item is MenuGroupProps {
    return "title" in item && item.title !== undefined;
  }

  // Listen for shortcut key combinations and trigger the onClick handler if the shortcut matches the key combination
  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      contents?.items.forEach((item) => {
        if (isGroup(item)) {
          item.items.forEach((item) => {
            if (item.shortcut === e.key) {
              item.onClick?.();
            }
          });
        } else {
          if (item.shortcut === e.key) {
            item.onClick?.();
          }
        }
      });
    }

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [contents]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button variant="default">{title}</Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="px-2" align="end">
        {/* <DropdownMenuLabel className="text-right">
          {contents?.title}
        </DropdownMenuLabel> */}
        {/* <DropdownMenuSeparator /> */}

        {contents?.items.map((item, idx) => {
          if (isGroup(item)) {
            return (
              <DropdownMenuGroup key={idx}>
                {item.title && (
                  <>
                    {/* If there is an item above the group, render a separator */}
                    {idx > 0 && <DropdownMenuSeparator />}
                    <DropdownMenuLabel>{item.title}</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                  </>
                )}
                {item.items.map((item, index) => (
                  <DropdownMenuItem
                    className="items-end"
                    key={index}
                    onClick={item.onClick}
                    disabled={item.disabled}
                  >
                    <span className="w-full text-end">{item.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            );
          } else {
            return (
              <>
                <DropdownMenuItem
                  className="items-end"
                  key={idx}
                  onClick={item.onClick}
                  disabled={item.disabled}
                >
                  <span className="w-full text-end">{item.label}</span>
                </DropdownMenuItem>
                {idx < contents.items.length - 1 && <DropdownMenuSeparator />}
              </>
            );
          }
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Menu;
