// export const dogLicenseRenewalForm = {
//   name: "dogLicenseRenewalFormFull",
//   description: "Dog License Renewal Form",
//   pages: [
//     {
//       title: "Instructions",
//       optional: false,
//       sortOrder: 1,
//       conditionallyDisplay: [],
//       sections: [
//         {
//           title: null,
//           sortOrder: 1,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: null,
//               description: null,
//               fieldName: "instructionHeader",
//               type: "html",
//               defaultValue:
//                 "<h3>Please complete the following steps to renew a dog license with expired documents.</h3>",
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: null,
//               description: null,
//               fieldName: "instructionFooter",
//               type: "html",
//               defaultValue:
//                 "<p>Attention: If you are renewing a dog license for a dog that has been spayed or neutered, you must provide a copy of the spay/neuter certificate from your veterinarian. You can upload the certificate at the end of this form.</p>",
//               sortOrder: 2,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//       ],
//       onPageNext: [],
//       onFormSubmit: [],
//     },
//     {
//       title: "Renewal Documents",
//       optional: false,
//       sortOrder: 2,
//       conditionallyDisplay: [],
//       sections: [
//         {
//           title: null,
//           sortOrder: 1,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: null,
//               description: null,
//               fieldName: "renewalDocHeader",
//               type: "html",
//               defaultValue: "<h3>Check the documents that are expired.</h3>",
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: null,
//               description: null,
//               fieldName: "renewalDocFooter",
//               type: "html",
//               defaultValue:
//                 "<p>Ensure uploaded documents are complete and accurate to avoid delays. Incomplete or incorrect submissions may result in your license being put on hold.</p>",
//               sortOrder: 2,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Renewal Documents Upload",
//               description: "Copy of the updated license documents.",
//               fieldName: "renewalDocumentsUpload",
//               type: "fileOptions",
//               defaultValue: null,
//               sortOrder: 4,
//               size: "full",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [
//                 {
//                   label: "Select",
//                   value: "",
//                   sortOrder: 1,
//                   description: "Select a file to upload.",
//                   group: null,
//                   default: true,
//                 },
//                 {
//                   label: "Rabies Certificate",
//                   value: "vaccineRecord",
//                   sortOrder: 2,
//                   description:
//                     "Please upload a copy of the dog's rabies certificate.",
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "Purebred Certificate",
//                   value: "purebredCert",
//                   sortOrder: 3,
//                   description: "Copy of the dog's purebred certificate.",
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "Service Dog Certificate",
//                   value: "serviceDogCert",
//                   sortOrder: 4,
//                   description: "Copy of the dog's service dog certificate.",
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "License Exemption Document",
//                   value: "exemptionDocuments",
//                   sortOrder: 5,
//                   description: "Copy of the dog's license exemption document.",
//                   group: null,
//                   default: false,
//                 },
//               ],
//               columnOrCustomFieldName: "renewalDocumentsUpload",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//       ],
//       onPageNext: [
//         {
//           api: "/coordinator/license/multiFormBuilder/dogLicenseRenewalFormFull/draft/{entityId}",
//           method: "PATCH",
//           requestSlug: [
//             {
//               key: "{entityId}",
//               valueLocation: "queryString",
//               valueName: "entityId",
//             },
//           ],
//           queryString: [],
//           body: {
//             type: "form-data",
//             sendAllFormDataField: true,
//             fields: [],
//           },
//           response: {
//             success: {
//               code: 204,
//               waitUntilComplete: false,
//               setAllToForm: false,
//               navigate: null,
//               fields: [],
//             },
//             error: {
//               code: [400, 500],
//               message: "Some error message here for toast",
//             },
//           },
//         },
//         {
//           api: "/coordinator/textextractor/bulk",
//           method: "POST",
//           requestSlug: [],
//           queryString: [],
//           body: {
//             type: "form-data",
//             sendAllFormDataField: false,
//             fields: [
//               {
//                 key: "exemptionDocuments",
//                 valueLocation: "form",
//                 valueName: "exemptionDocuments",
//               },
//               {
//                 key: "purebredCert",
//                 valueLocation: "form",
//                 valueName: "purebredCert",
//               },
//               {
//                 key: "serviceDogCert",
//                 valueLocation: "form",
//                 valueName: "serviceDogCert",
//               },
//               {
//                 key: "vaccineRecord",
//                 valueLocation: "form",
//                 valueName: "vaccineRecord",
//               },
//             ],
//           },
//           response: {
//             success: {
//               code: 200,
//               waitUntilComplete: true,
//               setAllToForm: true,
//               navigate: null,
//               fields: [],
//             },
//             error: {
//               code: [400, 500],
//               message: "Some error message here for toast",
//             },
//           },
//         },
//       ],
//       onFormSubmit: [],
//     },
//     {
//       title: "Health Information",
//       optional: false,
//       sortOrder: 3,
//       conditionallyDisplay: [],
//       sections: [
//         {
//           title: "Dog Health Information",
//           sortOrder: 1,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: "Vet / Hospital Name",
//               description: null,
//               fieldName: "veterinaryName",
//               type: "text",
//               defaultValue: null,
//               sortOrder: 1,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: "Please enter a valid name.",
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: {
//                 value: "^[A-Za-z\\s'-.,]*$",
//                 message: "Please enter a valid name.",
//               },
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "veterinaryName",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [
//                 {
//                   tableName: "participant",
//                   columnName: "participant_type_group_id",
//                   value: 7,
//                 },
//               ],
//             },
//             {
//               label: "Veterinarian Name",
//               description: null,
//               fieldName: "veterinarianName",
//               type: "text",
//               defaultValue: null,
//               sortOrder: 2,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: {
//                 value: "^[A-Za-z\\s'-.,]*$",
//                 message: "Please enter a valid name.",
//               },
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "veterinarianName",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Rabies Tag Number",
//               description: null,
//               fieldName: "rabiesTagNumber",
//               type: "text",
//               defaultValue: null,
//               sortOrder: 3,
//               size: "full",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: {
//                 value: "^[a-zA-Z0-9\\s]*$",
//                 message: "Please enter a valid rabies tag number.",
//               },
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "rabiesTagNumber",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Name",
//               description: null,
//               fieldName: "vaccineName",
//               type: "select",
//               defaultValue: "rabies",
//               sortOrder: 4,
//               size: "md",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [
//                 {
//                   label: "Select",
//                   value: "",
//                   sortOrder: 1,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "Rabies",
//                   value: "rabies",
//                   sortOrder: 2,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//               ],
//               columnOrCustomFieldName: "vaccineName",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Producer",
//               description: null,
//               fieldName: "vaccineProducer",
//               type: "select",
//               defaultValue: null,
//               sortOrder: 5,
//               size: "md",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [
//                 {
//                   label: "Merck Animal Health",
//                   value: "Merck Animal Health",
//                   sortOrder: 1,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//                 {
//                   label: "Zoetis",
//                   value: "Zoetis",
//                   sortOrder: 2,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//                 {
//                   label: "Boehringer Ingelheim",
//                   value: "Boehringer Ingelheim",
//                   sortOrder: 3,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//                 {
//                   label: "Elanco",
//                   value: "Elanco",
//                   sortOrder: 4,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//                 {
//                   label: "Virbac",
//                   value: "Virbac",
//                   sortOrder: 5,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//                 {
//                   label: "Other",
//                   value: "Other",
//                   sortOrder: 6,
//                   description: "",
//                   group: "",
//                   default: false,
//                 },
//               ],
//               columnOrCustomFieldName: "vaccineProducer",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Brand",
//               description: null,
//               fieldName: "vaccineBrand",
//               type: "select",
//               defaultValue: null,
//               sortOrder: 6,
//               size: "md",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [
//                 {
//                   label: "Select",
//                   value: "",
//                   sortOrder: 1,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "IMRAB 1",
//                   value: "IMRAB 1",
//                   sortOrder: 2,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "IMRAB 1 (TF)",
//                   value: "IMRAB 1 (TF)",
//                   sortOrder: 3,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "IMRAB 3",
//                   value: "IMRAB 3",
//                   sortOrder: 4,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "IMRAB 3 (TF)",
//                   value: "IMRAB 3 (TF)",
//                   sortOrder: 5,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "PUREVAX 1",
//                   value: "PUREVAX 1",
//                   sortOrder: 6,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "PUREVAX 3",
//                   value: "PUREVAX 3",
//                   sortOrder: 7,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "NOBIVAC 1",
//                   value: "NOBIVAC 1",
//                   sortOrder: 8,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "NOBIVAC 3 CA",
//                   value: "NOBIVAC 3 CA",
//                   sortOrder: 9,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "NOBIVAC 3",
//                   value: "NOBIVAC 3",
//                   sortOrder: 10,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "RABVAC 1",
//                   value: "RABVAC 1",
//                   sortOrder: 11,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "RABVAC 3",
//                   value: "RABVAC 3",
//                   sortOrder: 12,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "DEFENSOR 1",
//                   value: "DEFENSOR 1",
//                   sortOrder: 13,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "DEFENSOR 3",
//                   value: "DEFENSOR 3",
//                   sortOrder: 14,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//               ],
//               columnOrCustomFieldName: "vaccineBrand",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Dates are Exempt",
//               description: null,
//               fieldName: "vaccineDatesExempt",
//               type: "conditionalCheckbox",
//               defaultValue: "false",
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: true,
//               required: null,
//               minValue: null,
//               minLength: null,
//               maxValue: null,
//               maxLength: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               options: [],
//             },
//             {
//               label: "Vaccine Administered Date",
//               description: null,
//               fieldName: "vaccineAdministeredDate",
//               type: "date",
//               defaultValue: null,
//               sortOrder: 7,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: "Please enter a valid date.",
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [
//                 {
//                   fieldName: "vaccineDatesExempt",
//                   values: ["true"],
//                 },
//               ],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "vaccineAdministeredDate",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Expiration Date",
//               description: null,
//               fieldName: "vaccineDueDate",
//               type: "date",
//               defaultValue: null,
//               sortOrder: 8,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: "Please enter a valid date.",
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [
//                 {
//                   fieldName: "vaccineDatesExempt",
//                   values: ["true"],
//                 },
//               ],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "vaccineDueDate",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Lot/Serial Number",
//               description: null,
//               fieldName: "vaccineLotNumber",
//               type: "text",
//               defaultValue: "",
//               sortOrder: 9,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "vaccineLotNumber",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Lot/Serial Expiration",
//               description: null,
//               fieldName: "vaccineLotExpirationDate",
//               type: "date",
//               defaultValue: null,
//               sortOrder: 10,
//               size: "lg",
//               info: null,
//               google: false,
//               required: {
//                 value: false,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: "vaccineLotExpirationDate",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//         {
//           title: "Additional Information",
//           sortOrder: 2,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: "Spayed or Neutered?",
//               description: null,
//               fieldName: "dogSpayedOrNeutered",
//               type: "select",
//               defaultValue: null,
//               sortOrder: 6,
//               size: "full",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: null,
//               },
//               minValue: null,
//               minLength: null,
//               maxValue: null,
//               maxLength: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               options: [
//                 {
//                   label: "Select",
//                   value: "",
//                   sortOrder: 1,
//                   default: false,
//                 },
//                 {
//                   label: "Yes",
//                   value: "yes",
//                   sortOrder: 2,
//                   default: false,
//                 },
//                 {
//                   label: "No",
//                   value: "no",
//                   sortOrder: 3,
//                   default: false,
//                 },
//               ],
//             },
//           ],
//         },
//         {
//           title: null,
//           sortOrder: 5,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: null,
//               description: null,
//               fieldName: "serviceDogCert",
//               type: "fileDisplay",
//               defaultValue: null,
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//       ],
//       onPageNext: [
//         {
//           api: "/coordinator/license/multiFormBuilder/dogLicenseRenewalFormFull/draft/{entityId}",
//           method: "PATCH",
//           requestSlug: [
//             {
//               key: "{entityId}",
//               valueLocation: "queryString",
//               valueName: "entityId",
//             },
//           ],
//           queryString: [],
//           body: {
//             type: "form-data",
//             sendAllFormDataField: true,
//             fields: [],
//           },
//           response: {
//             success: {
//               code: 204,
//               waitUntilComplete: false,
//               setAllToForm: false,
//               navigate: null,
//               fields: [],
//             },
//             error: {
//               code: [400, 500],
//               message: "Some error message here for toast",
//             },
//           },
//         },
//       ],
//       onFormSubmit: [],
//     },
//     {
//       title: "License Duration",
//       optional: false,
//       sortOrder: 4,
//       conditionallyDisplay: [],
//       sections: [
//         {
//           title: "Select License Duration",
//           sortOrder: 1,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: null,
//               description: null,
//               fieldName: "durationHeader",
//               type: "html",
//               defaultValue:
//                 "<h3>Please select the duration of the license.</h3>",
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: null,
//               description: null,
//               fieldName: "durationFooter",
//               type: "html",
//               defaultValue:
//                 "<p>Attention: Maximum of 3 year per dog license unless dogs are purebred.</p>",
//               sortOrder: 2,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "License Duration",
//               description: null,
//               fieldName: "licenseDuration",
//               type: "select",
//               defaultValue: "1",
//               sortOrder: 3,
//               size: "full",
//               info: null,
//               google: false,
//               required: {
//                 value: true,
//                 message: null,
//               },
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [
//                 {
//                   label: "Select",
//                   value: "",
//                   sortOrder: 1,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "1 Year",
//                   value: "1",
//                   sortOrder: 2,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "2 Years",
//                   value: "2",
//                   sortOrder: 3,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//                 {
//                   label: "3 Years",
//                   value: "3",
//                   sortOrder: 4,
//                   description: null,
//                   group: null,
//                   default: false,
//                 },
//               ],
//               columnOrCustomFieldName: "licenseDuration",
//               tableName: "license",
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//       ],
//       onPageNext: [],
//       onFormSubmit: [],
//     },
//     {
//       title: "Confirm Information",
//       optional: false,
//       sortOrder: 5,
//       conditionallyDisplay: [],
//       sections: [
//         {
//           title: "License Duration",
//           sortOrder: 1,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: "License Duration",
//               description: null,
//               fieldName: "licenseDuration",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//         {
//           title: "Health Information",
//           sortOrder: 2,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: "Vet / Hospital Name",
//               description: null,
//               fieldName: "veterinaryName",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Veterinarian Name",
//               description: null,
//               fieldName: "veterinarianName",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 2,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Rabies Tag Number",
//               description: null,
//               fieldName: "rabiesTagNumber",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 3,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Name",
//               description: null,
//               fieldName: "vaccineName",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 4,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Producer",
//               description: null,
//               fieldName: "vaccineProducer",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 5,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Administered Date",
//               description: null,
//               fieldName: "vaccineAdministeredDate",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 6,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Vaccine Expiration Date",
//               description: null,
//               fieldName: "vaccineDueDate",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 7,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Lot/Serial Number",
//               description: null,
//               fieldName: "vaccineLotNumber",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 8,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//             {
//               label: "Lot/Serial Expiration",
//               description: null,
//               fieldName: "vaccineLotExpirationDate",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 9,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//         {
//           title: "Additional Information",
//           sortOrder: 3,
//           conditionallyDisplay: [],
//           elements: [
//             {
//               label: "Spayed or Neutered?",
//               description: null,
//               fieldName: "dogSpayedOrNeutered",
//               type: "fieldDisplay",
//               defaultValue: null,
//               sortOrder: 1,
//               size: "full",
//               info: null,
//               google: false,
//               required: null,
//               minLength: null,
//               minValue: null,
//               maxLength: null,
//               maxValue: null,
//               pattern: null,
//               conditionallyDisplay: [],
//               triggers: [],
//               options: [],
//               columnOrCustomFieldName: null,
//               tableName: null,
//               argumentTemplate: null,
//               arguments: [],
//             },
//           ],
//         },
//       ],
//       onPageNext: [],
//       onFormSubmit: [
//         {
//           api: "/license/license/{entityId}/renew",
//           method: "POST",
//           requestSlug: [
//             {
//               key: "{entityId}",
//               valueLocation: "queryString",
//               valueName: "entityId",
//             },
//           ],
//           queryString: [
//             {
//               key: "duration",
//               valueLocation: "form",
//               valueName: "licenseDuration",
//             },
//           ],
//           body: null,
//           response: {
//             success: {
//               code: 204,
//               waitUntilComplete: true,
//               setAllToForm: false,
//               navigate: "addToCart,cart",
//               fields: [],
//             },
//             error: {
//               code: [400, 500],
//               message: "Fail to renew license.",
//             },
//           },
//         },
//       ],
//     },
//   ],
//   requiredFields: [
//     {
//       key: "{entityId}",
//       valueLocation: "queryString",
//       valueName: "entityId",
//     },
//   ],
// };

export const dogLicenseRenewalForm = {
  name: "dogLicenseRenewalFormFull",
  description: "Dog License Renewal Form",
  pages: [
    {
      title: "Health Information",
      optional: false,
      sortOrder: 3,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Dog Health Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Vet / Hospital Name",
              description: null,
              fieldName: "veterinaryName",
              type: "text",
              defaultValue: null,
              sortOrder: 1,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid name.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[A-Za-z\\s'-.,]*$",
                message: "Please enter a valid name.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "veterinaryName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant",
                  columnName: "participant_type_group_id",
                  value: 7,
                },
              ],
            },
            {
              label: "Veterinarian Name",
              description: null,
              fieldName: "veterinarianName",
              type: "text",
              defaultValue: null,
              sortOrder: 2,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[A-Za-z\\s'-.,]*$",
                message: "Please enter a valid name.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "veterinarianName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Exempt from Vaccine",
              description: null,
              fieldName: "vaccineDatesExempt",
              type: "conditionalCheckbox",
              defaultValue: "false",
              sortOrder: 3,
              size: "full",
              info: null,
              google: true,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineDatesExempt",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Rabies Tag Number",
              description: null,
              fieldName: "rabiesTagNumber",
              type: "text",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s]*$",
                message: "Please enter a valid rabies tag number.",
              },
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "rabiesTagNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Name",
              description: null,
              fieldName: "vaccineName",
              type: "select",
              defaultValue: "rabies",
              sortOrder: 5,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Rabies",
                  value: "rabies",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Producer",
              description: null,
              fieldName: "vaccineProducer",
              type: "select",
              defaultValue: "",
              sortOrder: 6,
              size: "md",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Boehringer Ingelheim",
                  value: "Boehringer Ingelheim",
                  sortOrder: 2,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Elanco",
                  value: "Elanco",
                  sortOrder: 3,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merial",
                  value: "Merial",
                  sortOrder: 4,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Merck Animal Health",
                  value: "Merck Animal Health",
                  sortOrder: 5,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Virbac",
                  value: "Virbac",
                  sortOrder: 6,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Zoetis",
                  value: "Zoetis",
                  sortOrder: 7,
                  description: "",
                  group: "",
                  default: false,
                },
                {
                  label: "Other",
                  value: "Other",
                  sortOrder: 8,
                  description: "",
                  group: "",
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineProducer",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Brand",
              description: null,
              fieldName: "vaccineBrand",
              type: "select",
              defaultValue: null,
              sortOrder: 7,
              size: "md",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 1",
                  value: "IMRAB 1",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 1 (TF)",
                  value: "IMRAB 1 (TF)",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 3",
                  value: "IMRAB 3",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IMRAB 3 (TF)",
                  value: "IMRAB 3 (TF)",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "PUREVAX 1",
                  value: "PUREVAX 1",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "PUREVAX 3",
                  value: "PUREVAX 3",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 1",
                  value: "NOBIVAC 1",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 3 CA",
                  value: "NOBIVAC 3 CA",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "NOBIVAC 3",
                  value: "NOBIVAC 3",
                  sortOrder: 10,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "RABVAC 1",
                  value: "RABVAC 1",
                  sortOrder: 11,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "RABVAC 3",
                  value: "RABVAC 3",
                  sortOrder: 12,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "DEFENSOR 1",
                  value: "DEFENSOR 1",
                  sortOrder: 13,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "DEFENSOR 3",
                  value: "DEFENSOR 3",
                  sortOrder: 14,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Killed",
                  value: "Killed",
                  sortOrder: 15,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Killed Virus",
                  value: "Killed Virus",
                  sortOrder: 16,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "vaccineBrand",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Administered Date",
              description: null,
              fieldName: "vaccineAdministeredDate",
              type: "date",
              defaultValue: null,
              sortOrder: 8,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid date.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineAdministeredDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Expiration Date",
              description: null,
              fieldName: "vaccineDueDate",
              type: "date",
              defaultValue: null,
              sortOrder: 9,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: true,
                message: "Please enter a valid date.",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineDueDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Number",
              description: null,
              fieldName: "vaccineLotNumber",
              type: "text",
              defaultValue: "",
              sortOrder: 10,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineLotNumber",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Expiration",
              description: null,
              fieldName: "vaccineLotExpirationDate",
              type: "date",
              defaultValue: null,
              sortOrder: 11,
              size: "lg",
              info: null,
              google: false,
              required: {
                value: false,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [
                {
                  fieldName: "vaccineDatesExempt",
                  values: ["true"],
                },
              ],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "vaccineLotExpirationDate",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Additional Information",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Spayed or Neutered?",
              description: null,
              fieldName: "dogSpayedOrNeutered",
              type: "select",
              defaultValue: "",
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Yes",
                  value: "yes",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "No",
                  value: "no",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "dogSpayedOrNeutered",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: null,
          sortOrder: 5,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "serviceDogCert",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [],
      onFormSubmit: [],
    },
    {
      title: "License Duration",
      optional: false,
      sortOrder: 4,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Select License Duration",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "durationHeader",
              type: "html",
              defaultValue:
                "<h3>Please select the duration of the license.</h3>",
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: null,
              description: null,
              fieldName: "durationFooter",
              type: "html",
              defaultValue:
                "<p>Attention: Maximum of 3 year per dog license unless dogs are purebred.</p>",
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "License Duration",
              description: null,
              fieldName: "licenseDuration",
              type: "select",
              defaultValue: "",
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: {
                value: true,
                message: null,
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "1 Year",
                  value: "1",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "2 Years",
                  value: "2",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "3 Years",
                  value: "3",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "licenseDuration",
              tableName: "license",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [
        {
          api: "/coordinator/license/multiFormBuilder/dogLicenseRenewalFormFull/draft/{entityId}",
          method: "PATCH",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "queryString",
              valueName: "entityId",
            },
          ],
          queryString: [],
          body: {
            type: "form-data",
            sendAllFormDataField: true,
            fields: [],
          },
          response: {
            success: {
              code: 204,
              waitUntilComplete: false,
              setAllToForm: false,
              navigate: null,
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Some error message here for toast",
            },
          },
        },
      ],
      onFormSubmit: [],
    },
    {
      title: "Confirm Information",
      optional: false,
      sortOrder: 5,
      conditionallyDisplay: [],
      sections: [
        {
          title: "License Duration",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "License Duration",
              description: null,
              fieldName: "licenseDuration",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Health Information",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Vet / Hospital Name",
              description: null,
              fieldName: "veterinaryName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Veterinarian Name",
              description: null,
              fieldName: "veterinarianName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Rabies Tag Number",
              description: null,
              fieldName: "rabiesTagNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Name",
              description: null,
              fieldName: "vaccineName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Producer",
              description: null,
              fieldName: "vaccineProducer",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Administered Date",
              description: null,
              fieldName: "vaccineAdministeredDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Vaccine Expiration Date",
              description: null,
              fieldName: "vaccineDueDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 7,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Number",
              description: null,
              fieldName: "vaccineLotNumber",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 8,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Lot/Serial Expiration",
              description: null,
              fieldName: "vaccineLotExpirationDate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 9,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Additional Information",
          sortOrder: 3,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Spayed or Neutered?",
              description: null,
              fieldName: "dogSpayedOrNeutered",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [],
      onFormSubmit: [
        {
          api: "/license/license/{entityId}/renew",
          method: "POST",
          requestSlug: [
            {
              key: "{entityId}",
              valueLocation: "queryString",
              valueName: "entityId",
            },
          ],
          queryString: [
            {
              key: "duration",
              valueLocation: "form",
              valueName: "licenseDuration",
            },
          ],
          body: null,
          response: {
            success: {
              code: 204,
              waitUntilComplete: true,
              setAllToForm: false,
              navigate: "addToCart,cart",
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Fail to renew license.",
            },
          },
        },
      ],
    },
  ],
  requiredFields: [
    {
      key: "{entityId}",
      valueLocation: "queryString",
      valueName: "entityId",
    },
  ],
};
